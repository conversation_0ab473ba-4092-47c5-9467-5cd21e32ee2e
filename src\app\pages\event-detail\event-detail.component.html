<div class="container mx-auto px-4 py-12">
  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gold-600"></div>
  </div>

  <!-- Error Message -->
  <div *ngIf="errorMessage" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
    {{ errorMessage }}
  </div>

  <!-- Event Details -->
  <div *ngIf="!isLoading && event" class="grid grid-cols-1 lg:grid-cols-3 gap-8">
    <!-- Event Information -->
    <div class="lg:col-span-2">
      <div class="bg-white rounded-lg shadow-md overflow-hidden">
        <!-- Event Image Carousel -->
        <app-image-carousel
          [images]="event.images || []"
          [iconUrl]="event.iconUrl || null"
          [defaultImage]="event.imageUrl"
          [height]="'400px'"
          class="block w-full"
        ></app-image-carousel>
        <div class="p-6">
          <h1 class="text-3xl font-bold text-gray-900 mb-4">{{ event.title }}</h1>

          <div class="flex flex-wrap gap-4 mb-6">
            <div class="flex items-center text-gray-600">
              <span class="material-icons mr-2">event</span>
              <span>{{ formatDate(event.date) }}</span>
            </div>
            <div class="flex items-center text-gray-600">
              <span class="material-icons mr-2">schedule</span>
              <span>{{ event.time }}</span>
            </div>
            <div class="flex items-center text-gray-600">
              <span class="material-icons mr-2">person</span>
              <span>{{ event.maxCapacity }} places</span>
            </div>
            <div class="flex items-center text-gold-600 font-semibold">
              <span class="material-icons mr-2">payments</span>
              <span>{{ event.pricePerPerson }} TND par personne</span>
            </div>
          </div>

          <div class="prose max-w-none">
            <h2 class="text-xl font-semibold mb-2">Description</h2>
            <p class="text-gray-700">{{ event.description }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Reservation Section -->
    <div class="lg:col-span-1">
      <div class="bg-white rounded-lg shadow-md p-6 sticky top-4">
        <h2 class="text-2xl font-semibold mb-6">Réserver une table</h2>

        <!-- Authentication Check -->
        <div *ngIf="!isAuthenticated" class="bg-gray-100 p-4 rounded-md mb-6">
          <p class="text-gray-700 mb-4">Vous devez être connecté pour effectuer une réservation.</p>
          <button
            (click)="onLogin()"
            class="w-full bg-gold-600 hover:bg-gold-700 text-white py-2 px-4 rounded-md transition-colors"
          >
            Se connecter / S'inscrire
          </button>
        </div>

        <!-- Tables Section -->
        <div class="mt-8">
          <h3 class="text-xl font-semibold mb-4">Sélectionnez une table</h3>

          <div *ngIf="isLoading" class="flex justify-center p-4">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-gold-600"></div>
          </div>

          <div *ngIf="errorMessage" class="text-red-600 p-4 bg-red-50 rounded-md mb-4">
            {{ errorMessage }}
          </div>

          <div *ngIf="!isLoading && tables.length > 0" class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-3 mb-6">
            <button
              *ngFor="let table of tables"
              (click)="selectTable(table.id)"
              class="p-3 rounded-md text-center transition-colors"
              [ngClass]="{
                'bg-gold-600 text-white': selectedTableId === table.id,
                'bg-green-100 text-green-800 hover:bg-green-200': isTableAvailable(table) && selectedTableId !== table.id,
                'bg-red-100 text-red-800 cursor-not-allowed': !isTableAvailable(table)
              }"
              [disabled]="!isTableAvailable(table)"
            >
              <div class="font-medium">Table {{ table.tableNumber }}</div>
              <div class="text-sm">{{ table.capacity }} personnes</div>
              <div *ngIf="!isTableAvailable(table)" class="text-xs mt-1">Réservée</div>
            </button>
          </div>

          <div *ngIf="!isLoading && tables.length === 0" class="text-gray-600 p-4 bg-gray-100 rounded-md">
            <p>Aucune table disponible pour cet événement.</p>
            <p class="mt-2 text-sm">Veuillez contacter l'organisateur pour plus d'informations.</p>
          </div>
        </div>

        <!-- Reservation Form -->
        <div *ngIf="showReservationForm && selectedTable && event">
          <app-reservation-form
            [event]="event"
            [selectedTableId]="selectedTableId!"
            [selectedTable]="selectedTable"
            (formSubmit)="onReservationSubmit($event)"
          ></app-reservation-form>
        </div>
      </div>
    </div>
  </div>
</div>
