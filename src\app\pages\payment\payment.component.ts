import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { catchError, switchMap } from 'rxjs/operators';

import { PaymentFormComponent } from '../../components/payment-form/payment-form.component';
import { ReservationService } from '../../services/reservation.service';
import { Reservation } from '../../models/reservation.model';

@Component({
  selector: 'app-payment',
  imports: [CommonModule, PaymentFormComponent],
  templateUrl: './payment.component.html',
  styleUrl: './payment.component.scss'
})
export class PaymentComponent implements OnInit {
  reservation: Reservation | null = null;
  loading = true;
  error: string | null = null;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private reservationService: ReservationService
  ) {}

  ngOnInit(): void {
    this.loadReservation();
  }

  private loadReservation(): void {
    const reservationId = this.route.snapshot.paramMap.get('reservationId');

    if (!reservationId) {
      this.error = 'ID de réservation manquant';
      this.loading = false;
      return;
    }

    this.reservationService.getReservationById(reservationId).pipe(
      catchError(error => {
        console.error('Error loading reservation:', error);
        this.error = 'Impossible de charger la réservation';
        return of(null);
      })
    ).subscribe(reservation => {
      this.reservation = reservation;
      this.loading = false;

      if (!reservation) {
        this.error = 'Réservation non trouvée';
      } else if (reservation.status === 'confirmed') {
        // Redirect to success page if already paid
        this.router.navigate(['/success', reservationId]);
      }
    });
  }

  onPaymentSuccess(paymentId: string): void {
    if (!this.reservation) return;

    // Update reservation with payment information
    this.reservationService.updateReservationPayment(
      this.reservation.id,
      paymentId,
      'confirmed'
    ).subscribe({
      next: () => {
        this.router.navigate(['/success', this.reservation!.id]);
      },
      error: (error) => {
        console.error('Error updating reservation:', error);
        this.error = 'Erreur lors de la mise à jour de la réservation';
      }
    });
  }

  onPaymentError(errorMessage: string): void {
    this.error = errorMessage;
  }

  goBack(): void {
    if (this.reservation) {
      this.router.navigate(['/reservation', this.reservation.eventId]);
    } else {
      this.router.navigate(['/events']);
    }
  }
}
