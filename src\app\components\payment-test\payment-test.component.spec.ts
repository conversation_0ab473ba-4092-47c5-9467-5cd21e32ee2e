import { ComponentFixture, TestBed } from '@angular/core/testing';
import { FormsModule } from '@angular/forms';
import { of, throwError } from 'rxjs';

import { PaymentTestComponent } from './payment-test.component';
import { PaymentService } from '../../services/payment.service';

describe('PaymentTestComponent', () => {
  let component: PaymentTestComponent;
  let fixture: ComponentFixture<PaymentTestComponent>;
  let mockPaymentService: jasmine.SpyObj<PaymentService>;

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('PaymentService', [
      'createPaymentIntent',
      'confirmCardPayment',
      'createPayPalOrder',
      'capturePayPalOrder'
    ]);

    await TestBed.configureTestingModule({
      imports: [PaymentTestComponent, FormsModule],
      providers: [
        { provide: PaymentService, useValue: spy }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PaymentTestComponent);
    component = fixture.componentInstance;
    mockPaymentService = TestBed.inject(PaymentService) as jasmine.SpyObj<PaymentService>;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default test scenarios', () => {
    expect(component.testScenarios.length).toBeGreaterThan(0);
    expect(component.selectedScenario).toBe(component.testScenarios[0]);
    expect(component.useCustomValues).toBe(false);
  });

  describe('selectScenario', () => {
    it('should select a scenario and disable custom values', () => {
      const scenario = component.testScenarios[1];
      component.useCustomValues = true;
      
      component.selectScenario(scenario);
      
      expect(component.selectedScenario).toBe(scenario);
      expect(component.useCustomValues).toBe(false);
    });
  });

  describe('getCurrentAmount', () => {
    it('should return custom amount when useCustomValues is true', () => {
      component.useCustomValues = true;
      component.customAmount = 500;
      
      expect(component.getCurrentAmount()).toBe(500);
    });

    it('should return scenario amount when useCustomValues is false', () => {
      component.useCustomValues = false;
      component.selectedScenario = component.testScenarios[0];
      
      expect(component.getCurrentAmount()).toBe(component.testScenarios[0].amount);
    });
  });

  describe('getCurrentReservationId', () => {
    it('should return custom reservation ID when useCustomValues is true', () => {
      component.useCustomValues = true;
      component.customReservationId = 'custom-id';
      
      expect(component.getCurrentReservationId()).toBe('custom-id');
    });

    it('should return scenario reservation ID when useCustomValues is false', () => {
      component.useCustomValues = false;
      component.selectedScenario = component.testScenarios[0];
      
      expect(component.getCurrentReservationId()).toBe(component.testScenarios[0].reservationId);
    });
  });

  describe('onPaymentSuccess', () => {
    it('should add success result to testResults', () => {
      const paymentId = 'test-payment-123';
      const initialLength = component.testResults.length;
      
      component.onPaymentSuccess(paymentId);
      
      expect(component.testResults.length).toBe(initialLength + 1);
      expect(component.testResults[0].success).toBe(true);
      expect(component.testResults[0].paymentId).toBe(paymentId);
    });
  });

  describe('onPaymentError', () => {
    it('should add error result to testResults', () => {
      const error = 'Test error message';
      const initialLength = component.testResults.length;
      
      component.onPaymentError(error);
      
      expect(component.testResults.length).toBe(initialLength + 1);
      expect(component.testResults[0].success).toBe(false);
      expect(component.testResults[0].error).toBe(error);
    });
  });

  describe('testCreatePaymentIntent', () => {
    it('should test createPaymentIntent successfully', () => {
      const mockResult = { clientSecret: 'test-secret', paymentIntentId: 'pi-123' };
      mockPaymentService.createPaymentIntent.and.returnValue(of(mockResult));
      
      component.testCreatePaymentIntent();
      
      expect(mockPaymentService.createPaymentIntent).toHaveBeenCalled();
      expect(component.serviceTestResults[0].success).toBe(true);
      expect(component.serviceTestResults[0].method).toBe('createPaymentIntent');
    });

    it('should handle createPaymentIntent error', () => {
      mockPaymentService.createPaymentIntent.and.returnValue(
        throwError(() => new Error('Test error'))
      );
      
      component.testCreatePaymentIntent();
      
      expect(component.serviceTestResults[0].success).toBe(false);
      expect(component.serviceTestResults[0].error).toBe('Test error');
    });
  });

  describe('testCreatePayPalOrder', () => {
    it('should test createPayPalOrder successfully', () => {
      const mockResult = { orderId: 'order-123', approvalUrl: 'https://paypal.com' };
      mockPaymentService.createPayPalOrder.and.returnValue(of(mockResult));
      
      component.testCreatePayPalOrder();
      
      expect(mockPaymentService.createPayPalOrder).toHaveBeenCalled();
      expect(component.serviceTestResults[0].success).toBe(true);
      expect(component.serviceTestResults[0].method).toBe('createPayPalOrder');
    });
  });

  describe('testConfirmCardPayment', () => {
    it('should test confirmCardPayment successfully', () => {
      const mockPaymentIntent = { clientSecret: 'test-secret', paymentIntentId: 'pi-123' };
      const mockConfirmation = { success: true };
      
      mockPaymentService.createPaymentIntent.and.returnValue(of(mockPaymentIntent));
      mockPaymentService.confirmCardPayment.and.returnValue(of(mockConfirmation));
      
      component.testConfirmCardPayment();
      
      expect(mockPaymentService.createPaymentIntent).toHaveBeenCalled();
      expect(mockPaymentService.confirmCardPayment).toHaveBeenCalledWith('pi-123');
      expect(component.serviceTestResults[0].success).toBe(true);
      expect(component.serviceTestResults[0].method).toBe('confirmCardPayment');
    });

    it('should handle createPaymentIntent error in confirmCardPayment test', () => {
      mockPaymentService.createPaymentIntent.and.returnValue(
        throwError(() => new Error('Payment intent error'))
      );
      
      component.testConfirmCardPayment();
      
      expect(component.serviceTestResults[0].success).toBe(false);
      expect(component.serviceTestResults[0].method).toBe('confirmCardPayment (createPaymentIntent failed)');
    });
  });

  describe('testCapturePayPalOrder', () => {
    it('should test capturePayPalOrder successfully', () => {
      const mockOrder = { orderId: 'order-123', approvalUrl: 'https://paypal.com' };
      const mockCapture = { success: true };
      
      mockPaymentService.createPayPalOrder.and.returnValue(of(mockOrder));
      mockPaymentService.capturePayPalOrder.and.returnValue(of(mockCapture));
      
      component.testCapturePayPalOrder();
      
      expect(mockPaymentService.createPayPalOrder).toHaveBeenCalled();
      expect(mockPaymentService.capturePayPalOrder).toHaveBeenCalledWith('order-123');
      expect(component.serviceTestResults[0].success).toBe(true);
      expect(component.serviceTestResults[0].method).toBe('capturePayPalOrder');
    });
  });

  describe('clearResults', () => {
    it('should clear all test results', () => {
      component.testResults = [{ scenario: 'test', success: true, timestamp: new Date() }];
      component.serviceTestResults = [{ method: 'test', success: true, timestamp: new Date() }];
      
      component.clearResults();
      
      expect(component.testResults.length).toBe(0);
      expect(component.serviceTestResults.length).toBe(0);
    });
  });

  describe('exportResults', () => {
    it('should create and download results file', () => {
      // Mock URL and createElement
      const mockUrl = 'blob:test-url';
      const mockAnchor = {
        href: '',
        download: '',
        click: jasmine.createSpy('click')
      };
      
      spyOn(window.URL, 'createObjectURL').and.returnValue(mockUrl);
      spyOn(window.URL, 'revokeObjectURL');
      spyOn(document, 'createElement').and.returnValue(mockAnchor as any);
      
      component.exportResults();
      
      expect(window.URL.createObjectURL).toHaveBeenCalled();
      expect(mockAnchor.click).toHaveBeenCalled();
      expect(window.URL.revokeObjectURL).toHaveBeenCalledWith(mockUrl);
    });
  });
});
