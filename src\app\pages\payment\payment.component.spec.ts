import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute, Router } from '@angular/router';
import { of, throwError } from 'rxjs';
import { By } from '@angular/platform-browser';

import { PaymentComponent } from './payment.component';
import { ReservationService } from '../../services/reservation.service';
import { Reservation } from '../../models/reservation.model';

describe('PaymentComponent', () => {
  let component: PaymentComponent;
  let fixture: ComponentFixture<PaymentComponent>;
  let mockReservationService: jasmine.SpyObj<ReservationService>;
  let mockRouter: jasmine.SpyObj<Router>;
  let mockActivatedRoute: any;

  const mockReservation: Reservation = {
    id: 'test-reservation-123',
    userId: 'user-123',
    eventId: 'event-123',
    tableId: 'table-123',
    numberOfGuests: 4,
    status: 'pending',
    totalPrice: 200,
    paymentIntentId: '',
    specialRequests: '',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  };

  beforeEach(async () => {
    const reservationServiceSpy = jasmine.createSpyObj('ReservationService', [
      'getReservationById',
      'updateReservationPayment'
    ]);
    const routerSpy = jasmine.createSpyObj('Router', ['navigate']);

    mockActivatedRoute = {
      snapshot: {
        paramMap: {
          get: jasmine.createSpy('get').and.returnValue('test-reservation-123')
        }
      }
    };

    await TestBed.configureTestingModule({
      imports: [PaymentComponent],
      providers: [
        { provide: ReservationService, useValue: reservationServiceSpy },
        { provide: Router, useValue: routerSpy },
        { provide: ActivatedRoute, useValue: mockActivatedRoute }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PaymentComponent);
    component = fixture.componentInstance;
    mockReservationService = TestBed.inject(ReservationService) as jasmine.SpyObj<ReservationService>;
    mockRouter = TestBed.inject(Router) as jasmine.SpyObj<Router>;
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with loading state', () => {
    expect(component.loading).toBe(true);
    expect(component.reservation).toBeNull();
    expect(component.error).toBeNull();
  });

  describe('ngOnInit', () => {
    it('should load reservation on init', () => {
      mockReservationService.getReservationById.and.returnValue(of(mockReservation));

      component.ngOnInit();

      expect(mockReservationService.getReservationById).toHaveBeenCalledWith('test-reservation-123');
      expect(component.reservation).toEqual(mockReservation);
      expect(component.loading).toBe(false);
      expect(component.error).toBeNull();
    });

    it('should handle missing reservation ID', () => {
      mockActivatedRoute.snapshot.paramMap.get.and.returnValue(null);

      component.ngOnInit();

      expect(component.error).toBe('ID de réservation manquant');
      expect(component.loading).toBe(false);
      expect(mockReservationService.getReservationById).not.toHaveBeenCalled();
    });

    it('should handle reservation loading error', () => {
      mockReservationService.getReservationById.and.returnValue(
        throwError(() => new Error('Network error'))
      );

      component.ngOnInit();

      expect(component.error).toBe('Impossible de charger la réservation');
      expect(component.loading).toBe(false);
      expect(component.reservation).toBeNull();
    });

    it('should redirect to success page if reservation is already confirmed', () => {
      const confirmedReservation = { ...mockReservation, status: 'confirmed' as const };
      mockReservationService.getReservationById.and.returnValue(of(confirmedReservation));

      component.ngOnInit();

      expect(mockRouter.navigate).toHaveBeenCalledWith(['/success', 'test-reservation-123']);
    });

    it('should handle null reservation response', () => {
      mockReservationService.getReservationById.and.returnValue(of(null));

      component.ngOnInit();

      expect(component.error).toBe('Réservation non trouvée');
      expect(component.loading).toBe(false);
    });
  });

  describe('onPaymentSuccess', () => {
    beforeEach(() => {
      component.reservation = mockReservation;
    });

    it('should update reservation and navigate to success page', () => {
      mockReservationService.updateReservationPayment.and.returnValue(of(mockReservation));

      component.onPaymentSuccess('payment-123');

      expect(mockReservationService.updateReservationPayment).toHaveBeenCalledWith(
        'test-reservation-123',
        'payment-123',
        'confirmed'
      );
      expect(mockRouter.navigate).toHaveBeenCalledWith(['/success', 'test-reservation-123']);
    });

    it('should handle update reservation error', () => {
      mockReservationService.updateReservationPayment.and.returnValue(
        throwError(() => new Error('Update failed'))
      );

      component.onPaymentSuccess('payment-123');

      expect(component.error).toBe('Erreur lors de la mise à jour de la réservation');
      expect(mockRouter.navigate).not.toHaveBeenCalled();
    });

    it('should do nothing if no reservation is loaded', () => {
      component.reservation = null;

      component.onPaymentSuccess('payment-123');

      expect(mockReservationService.updateReservationPayment).not.toHaveBeenCalled();
      expect(mockRouter.navigate).not.toHaveBeenCalled();
    });
  });

  describe('onPaymentError', () => {
    it('should set error message', () => {
      const errorMessage = 'Payment failed';

      component.onPaymentError(errorMessage);

      expect(component.error).toBe(errorMessage);
    });
  });

  describe('goBack', () => {
    it('should navigate to reservation page when reservation is loaded', () => {
      component.reservation = mockReservation;

      component.goBack();

      expect(mockRouter.navigate).toHaveBeenCalledWith(['/reservation', 'event-123']);
    });

    it('should navigate to events page when no reservation is loaded', () => {
      component.reservation = null;

      component.goBack();

      expect(mockRouter.navigate).toHaveBeenCalledWith(['/events']);
    });
  });

  describe('Template Rendering', () => {
    it('should show loading state', () => {
      component.loading = true;
      fixture.detectChanges();

      const loadingElement = fixture.debugElement.query(By.css('.animate-spin'));
      expect(loadingElement).toBeTruthy();
    });

    it('should show error state', () => {
      component.loading = false;
      component.error = 'Test error';
      fixture.detectChanges();

      const errorElement = fixture.debugElement.query(By.css('.bg-red-50'));
      expect(errorElement).toBeTruthy();
      expect(errorElement.nativeElement.textContent).toContain('Test error');
    });

    it('should show reservation summary when loaded', () => {
      component.loading = false;
      component.reservation = mockReservation;
      fixture.detectChanges();

      const summaryElement = fixture.debugElement.query(By.css('h2'));
      expect(summaryElement.nativeElement.textContent).toContain('Résumé de la réservation');

      const totalElement = fixture.debugElement.query(By.css('.text-gold-600'));
      expect(totalElement.nativeElement.textContent).toContain('200 TND');
    });

    it('should show payment form when reservation is loaded', () => {
      component.loading = false;
      component.reservation = mockReservation;
      fixture.detectChanges();

      const paymentFormElement = fixture.debugElement.query(By.css('app-payment-form'));
      expect(paymentFormElement).toBeTruthy();
    });

    it('should show security notice when reservation is loaded', () => {
      component.loading = false;
      component.reservation = mockReservation;
      fixture.detectChanges();

      const securityElement = fixture.debugElement.query(By.css('.bg-blue-50'));
      expect(securityElement).toBeTruthy();
      expect(securityElement.nativeElement.textContent).toContain('Paiement sécurisé');
    });

    it('should display correct reservation status badge', () => {
      component.loading = false;
      component.reservation = mockReservation;
      fixture.detectChanges();

      const statusBadge = fixture.debugElement.query(By.css('.bg-yellow-100'));
      expect(statusBadge).toBeTruthy();
      expect(statusBadge.nativeElement.textContent.trim()).toBe('En attente');
    });
  });
});
