export interface Reservation {
  id: string;
  userId: string;
  eventId: string;
  tableId: string;
  numberOfGuests: number;
  status: 'pending' | 'confirmed' | 'cancelled';
  totalPrice: number;
  paymentIntentId?: string;
  specialRequests?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ReservationFormData {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  numberOfGuests: number;
  specialRequests?: string;
}
