<div class="bg-white rounded-lg shadow-md overflow-hidden transition-transform duration-300 hover:shadow-lg hover:-translate-y-1">
  <div class="relative h-48">
    <img
      [src]="event.imageUrl"
      [alt]="event.title"
      class="w-full h-full object-cover"
    />
  </div>
  <div class="p-6">
    <h3 class="text-xl font-semibold text-gray-900 mb-2">{{ event.title }}</h3>
    <div class="flex items-center text-gray-600 mb-2">
      <span class="material-icons mr-2">event</span>
      <span>{{ formatDate(event.date) }}</span>
    </div>
    <div class="flex items-center text-gray-600 mb-4">
      <span class="material-icons mr-2">schedule</span>
      <span>{{ event.time }}</span>
    </div>
    <p class="text-gray-600 mb-4 line-clamp-3">{{ event.description }}</p>
    <div class="flex justify-between items-center">
      <span class="text-gold-600 font-semibold">{{ event.pricePerPerson }} TND</span>
      <a
        [routerLink]="['/events', event.id]"
        class="bg-gold-600 hover:bg-gold-700 text-white px-4 py-2 rounded-md transition-colors"
      >
        Réserver
      </a>
    </div>
  </div>
</div>
