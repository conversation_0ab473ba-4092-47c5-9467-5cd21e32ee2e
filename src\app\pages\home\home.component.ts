import { Component, OnInit } from '@angular/core';
import { RouterModule } from '@angular/router';
import { CommonModule } from '@angular/common';
import { Event } from '../../models/event.model';
import { EventService } from '../../services/event.service';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [RouterModule, CommonModule],
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss'
})
export class HomeComponent implements OnInit {
  upcomingEvents: Event[] = [];

  constructor(private eventService: EventService) {}

  ngOnInit() {
    // Fetch events from the service
    this.eventService.getUpcomingEvents().subscribe({
      next: (events) => {
        this.upcomingEvents = events;
      },
      error: (error) => {
        console.error('Error loading upcoming events:', error);
        // Fallback to mock data if API call fails
        this.upcomingEvents = [
          {
            id: '1',
            title: 'Soirée Gastronomique Tunisienne',
            description: 'Découvrez les saveurs authentiques de la cuisine tunisienne lors de cette soirée exceptionnelle.',
            date: '2025-05-15',
            time: '19:30',
            imageUrl: 'assets/images/event1.jpg',
            restaurantId: '1',
            maxCapacity: 50,
            pricePerPerson: 120,
            createdAt: '2025-04-01',
            updatedAt: '2025-04-01'
          },
          {
            id: '2',
            title: 'Dîner Méditerranéen',
            description: 'Un voyage culinaire à travers la Méditerranée avec des plats frais et savoureux.',
            date: '2025-05-22',
            time: '20:00',
            imageUrl: 'assets/images/event2.jpg',
            restaurantId: '2',
            maxCapacity: 40,
            pricePerPerson: 150,
            createdAt: '2025-04-02',
            updatedAt: '2025-04-02'
          },
          {
            id: '3',
            title: 'Brunch de Luxe',
            description: 'Commencez votre journée avec un brunch de luxe comprenant une sélection de mets raffinés.',
            date: '2025-05-25',
            time: '11:00',
            imageUrl: 'assets/images/event3.jpg',
            restaurantId: '3',
            maxCapacity: 30,
            pricePerPerson: 90,
            createdAt: '2025-04-03',
            updatedAt: '2025-04-03'
          }
        ];
      }
    });
  }

  formatDate(dateString: string): string {
    const date = new Date(dateString);
    return date.toLocaleDateString('fr-FR', { day: 'numeric', month: 'long', year: 'numeric' });
  }
}
