import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { ReservationService } from './reservation.service';

interface PaymentIntent {
  id: string;
  amount: number;
  status: 'requires_payment_method' | 'requires_confirmation' | 'succeeded' | 'canceled';
  client_secret?: string;
}

@Injectable({
  providedIn: 'root'
})
export class PaymentService {
  private paymentIntents: PaymentIntent[] = [];

  constructor(private reservationService: ReservationService) { }

  createPaymentIntent(amount: number, reservationId: string): Observable<{ clientSecret: string, paymentIntentId: string }> {
    // Dans une application réelle, nous ferions une requête API vers Stripe ici
    const paymentIntent: PaymentIntent = {
      id: `pi_${Date.now()}`,
      amount,
      status: 'requires_payment_method',
      client_secret: `pi_${Date.now()}_secret_${Math.random().toString(36).substring(2, 15)}`
    };

    this.paymentIntents.push(paymentIntent);

    return of({
      clientSecret: paymentIntent.client_secret || '',
      paymentIntentId: paymentIntent.id
    });
  }

  confirmCardPayment(paymentIntentId: string): Observable<{ success: boolean, error?: string }> {
    // Dans une application réelle, nous ferions une requête API vers Stripe ici
    const paymentIntentIndex = this.paymentIntents.findIndex(pi => pi.id === paymentIntentId);

    if (paymentIntentIndex === -1) {
      return of({ success: false, error: 'Payment intent not found' });
    }

    // Simuler un paiement réussi
    this.paymentIntents[paymentIntentIndex] = {
      ...this.paymentIntents[paymentIntentIndex],
      status: 'succeeded'
    };

    return of({ success: true });
  }

  createPayPalOrder(amount: number, reservationId: string): Observable<{ orderId: string, approvalUrl: string }> {
    // Dans une application réelle, nous ferions une requête API vers PayPal ici
    return of({
      orderId: `order_${Date.now()}`,
      approvalUrl: `https://www.sandbox.paypal.com/checkoutnow?token=EC-${Date.now()}`
    });
  }

  capturePayPalOrder(orderId: string): Observable<{ success: boolean, error?: string }> {
    // Dans une application réelle, nous ferions une requête API vers PayPal ici
    return of({ success: true });
  }
}
