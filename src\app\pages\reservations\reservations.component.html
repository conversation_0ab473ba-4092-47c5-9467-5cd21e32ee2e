<div class="container mx-auto px-4 py-12">
  <h1 class="text-3xl font-bold text-center mb-8">Mes R<PERSON>ervations</h1>

  <!-- Success Message -->
  <div *ngIf="successMessage" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
    {{ successMessage }}
  </div>

  <!-- Error Message -->
  <div *ngIf="errorMessage" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
    {{ errorMessage }}
  </div>

  <!-- Loading Indicator -->
  <div *ngIf="isLoading" class="flex justify-center items-center py-12">
    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-gold-600"></div>
  </div>

  <!-- No Reservations -->
  <div *ngIf="!isLoading && reservations.length === 0" class="text-center py-12">
    <p class="text-gray-600 text-lg mb-6">Vous n'avez pas encore de réservations.</p>
    <a routerLink="/events" class="bg-gold-600 hover:bg-gold-700 text-white px-6 py-3 rounded-md transition-colors inline-block">
      Découvrir les événements
    </a>
  </div>

  <!-- Reservations List -->
  <div *ngIf="!isLoading && reservations.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    <div *ngFor="let reservation of reservations" class="bg-white rounded-lg shadow-md overflow-hidden">
      <div class="p-6">
        <div class="flex justify-between items-start mb-4">
          <h3 class="text-xl font-semibold text-gray-900">
            {{ getEventForReservation(reservation)?.title || 'Événement inconnu' }}
          </h3>
          <span
            class="px-3 py-1 rounded-full text-sm font-medium"
            [ngClass]="getStatusClass(reservation.status)"
          >
            {{ getStatusLabel(reservation.status) }}
          </span>
        </div>

        <div class="space-y-2 mb-4">
          <div class="flex items-center text-gray-600">
            <span class="material-icons mr-2">event</span>
            <span>{{ getEventForReservation(reservation)?.date ? formatDate(getEventForReservation(reservation)!.date) : 'Date inconnue' }}</span>
          </div>
          <div class="flex items-center text-gray-600">
            <span class="material-icons mr-2">schedule</span>
            <span>{{ getEventForReservation(reservation)?.time || 'Heure inconnue' }}</span>
          </div>
          <div class="flex items-center text-gray-600">
            <span class="material-icons mr-2">people</span>
            <span>{{ reservation.numberOfGuests }} {{ reservation.numberOfGuests > 1 ? 'personnes' : 'personne' }}</span>
          </div>
          <div class="flex items-center text-gold-600 font-semibold">
            <span class="material-icons mr-2">payments</span>
            <span>{{ reservation.totalPrice }} TND</span>
          </div>
        </div>

        <div class="flex justify-between">
          <button
            (click)="showReservationDetails(reservation)"
            class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md transition-colors"
          >
            Détails
          </button>
          <button
            *ngIf="canCancelReservation(reservation)"
            (click)="confirmCancelReservation(reservation)"
            class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md transition-colors"
          >
            Annuler
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Reservation Details Modal -->
  <div *ngIf="selectedReservation && !showCancelConfirmation" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
      <div class="p-6">
        <div class="flex justify-between items-center mb-6">
          <h2 class="text-2xl font-semibold">Détails de la réservation</h2>
          <button
            (click)="closeReservationDetails()"
            class="text-gray-500 hover:text-gray-700"
          >
            <span class="material-icons">close</span>
          </button>
        </div>

        <div class="space-y-6">
          <div>
            <h3 class="text-lg font-medium mb-2">Événement</h3>
            <p class="text-gray-700">{{ getEventForReservation(selectedReservation)?.title || 'Événement inconnu' }}</p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 class="text-lg font-medium mb-2">Date et heure</h3>
              <p class="text-gray-700">
                {{ getEventForReservation(selectedReservation)?.date ? formatDate(getEventForReservation(selectedReservation)!.date) : 'Date inconnue' }}
                à {{ getEventForReservation(selectedReservation)?.time || 'Heure inconnue' }}
              </p>
            </div>

            <div>
              <h3 class="text-lg font-medium mb-2">Statut</h3>
              <p
                class="inline-block px-3 py-1 rounded-full text-sm font-medium"
                [ngClass]="getStatusClass(selectedReservation.status)"
              >
                {{ getStatusLabel(selectedReservation.status) }}
              </p>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h3 class="text-lg font-medium mb-2">Nombre de personnes</h3>
              <p class="text-gray-700">{{ selectedReservation.numberOfGuests }} {{ selectedReservation.numberOfGuests > 1 ? 'personnes' : 'personne' }}</p>
            </div>

            <div>
              <h3 class="text-lg font-medium mb-2">Prix total</h3>
              <p class="text-gray-700 font-semibold">{{ selectedReservation.totalPrice }} TND</p>
            </div>
          </div>

          <div>
            <h3 class="text-lg font-medium mb-2">Référence de réservation</h3>
            <p class="text-gray-700">{{ selectedReservation.id }}</p>
          </div>

          <div>
            <h3 class="text-lg font-medium mb-2">Date de réservation</h3>
            <p class="text-gray-700">{{ formatDate(selectedReservation.createdAt) }}</p>
          </div>
        </div>

        <div class="mt-8 flex justify-end">
          <button
            *ngIf="canCancelReservation(selectedReservation)"
            (click)="confirmCancelReservation(selectedReservation)"
            class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md transition-colors"
          >
            Annuler la réservation
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Cancel Confirmation Modal -->
  <div *ngIf="showCancelConfirmation && selectedReservation" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
    <div class="bg-white rounded-lg shadow-lg max-w-md w-full">
      <div class="p-6">
        <h2 class="text-xl font-semibold mb-4">Confirmer l'annulation</h2>
        <p class="text-gray-700 mb-6">
          Êtes-vous sûr de vouloir annuler votre réservation pour l'événement
          <span class="font-medium">{{ getEventForReservation(selectedReservation)?.title || 'Événement inconnu' }}</span> ?
          Cette action est irréversible.
        </p>

        <div class="flex justify-end space-x-4">
          <button
            (click)="closeCancelConfirmation()"
            class="bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md transition-colors"
          >
            Retour
          </button>
          <button
            (click)="cancelReservation()"
            class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-md transition-colors"
          >
            Confirmer l'annulation
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
