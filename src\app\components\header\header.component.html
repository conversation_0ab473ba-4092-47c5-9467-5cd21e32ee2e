<header class="bg-white shadow-md">
  <div class="container mx-auto px-4 py-4 flex justify-between items-center">
    <a routerLink="/" class="flex items-center">
      <img src="/assets/images/logo.png" alt="The Residence Tunis" class="h-12 w-auto">
    </a>

    <!-- Desktop Navigation -->
    <nav class="hidden md:flex space-x-8">
      <a routerLink="/" routerLinkActive="text-gold-600" [routerLinkActiveOptions]="{exact: true}" class="text-gray-800 hover:text-gold-600 font-medium">
        Accueil
      </a>
      <a routerLink="/events" routerLinkActive="text-gold-600" class="text-gray-800 hover:text-gold-600 font-medium">
        Événements
      </a>
      <a routerLink="/account" routerLinkActive="text-gold-600" class="text-gray-800 hover:text-gold-600 font-medium">
        Mon Compte
      </a>
      <a routerLink="/account/reservations" routerLinkActive="text-gold-600" class="text-gray-800 hover:text-gold-600 font-medium">
        Mes <PERSON>
      </a>
      <a routerLink="/contact" routerLinkActive="text-gold-600" class="text-gray-800 hover:text-gold-600 font-medium">
        Contact
      </a>
    </nav>

    <!-- Mobile Menu Button -->
    <button
      class="md:hidden text-gray-800"
      (click)="toggleMenu()"
    >
      <span class="material-icons">{{ isMenuOpen ? 'close' : 'menu' }}</span>
    </button>
  </div>

  <!-- Mobile Navigation -->
  <div *ngIf="isMenuOpen" class="md:hidden bg-white py-4 px-4 shadow-lg">
    <nav class="flex flex-col space-y-4">
      <a
        routerLink="/"
        routerLinkActive="text-gold-600"
        [routerLinkActiveOptions]="{exact: true}"
        class="text-gray-800 hover:text-gold-600 font-medium"
        (click)="toggleMenu()"
      >
        Accueil
      </a>
      <a
        routerLink="/events"
        routerLinkActive="text-gold-600"
        class="text-gray-800 hover:text-gold-600 font-medium"
        (click)="toggleMenu()"
      >
        Événements
      </a>
      <a
        routerLink="/account"
        routerLinkActive="text-gold-600"
        class="text-gray-800 hover:text-gold-600 font-medium"
        (click)="toggleMenu()"
      >
        Mon Compte
      </a>
      <a
        routerLink="/account/reservations"
        routerLinkActive="text-gold-600"
        class="text-gray-800 hover:text-gold-600 font-medium"
        (click)="toggleMenu()"
      >
        Mes Réservations
      </a>
      <a
        routerLink="/contact"
        routerLinkActive="text-gold-600"
        class="text-gray-800 hover:text-gold-600 font-medium"
        (click)="toggleMenu()"
      >
        Contact
      </a>
    </nav>
  </div>
</header>
