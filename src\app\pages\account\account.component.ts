import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router, RouterModule } from '@angular/router';
import { AuthService } from '../../services/auth.service';
import { User } from '../../models/user.model';

@Component({
  selector: 'app-account',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule, RouterModule],
  templateUrl: './account.component.html',
  styleUrl: './account.component.scss'
})
export class AccountComponent implements OnInit {
  activeTab: 'login' | 'register' | 'profile' | 'forgot-password' = 'login';
  loginForm!: FormGroup;
  registerForm!: FormGroup;
  profileForm!: FormGroup;
  forgotPasswordForm!: FormGroup;
  isSubmitting = false;
  errorMessage = '';
  successMessage = '';
  currentUser: User | null = null;

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.initForms();
    this.checkAuthStatus();
  }

  private checkAuthStatus(): void {
    this.currentUser = this.authService.getCurrentUser();
    if (this.currentUser) {
      this.activeTab = 'profile';
      this.populateProfileForm();
    }
  }

  private initForms(): void {
    // Custom validators
    const nameValidator = Validators.pattern(/^[a-zA-ZÀ-ÿ\s'-]+$/);
    const phoneValidator = Validators.pattern(/^[+]?[(]?[0-9]{1,4}[)]?[-\s.]?[0-9]{1,4}[-\s.]?[0-9]{1,9}$/);
    const passwordValidator = Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[A-Za-z\d@$!%*?&#]{8,}$/);

    this.loginForm = this.fb.group({
      email: ['', [
        Validators.required,
        Validators.email,
        Validators.maxLength(100)
      ]],
      password: ['', [
        Validators.required,
        Validators.minLength(6),
        Validators.maxLength(50)
      ]]
    });

    this.registerForm = this.fb.group({
      firstName: ['', [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(50),
        nameValidator
      ]],
      lastName: ['', [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(50),
        nameValidator
      ]],
      email: ['', [
        Validators.required,
        Validators.email,
        Validators.maxLength(100)
      ]],
      phoneNumber: ['', [
        Validators.required,
        Validators.minLength(8),
        Validators.maxLength(20),
        phoneValidator
      ]],
      password: ['', [
        Validators.required,
        Validators.minLength(8),
        Validators.maxLength(50),
        passwordValidator
      ]],
      confirmPassword: ['', [
        Validators.required
      ]]
    }, { validators: this.passwordMatchValidator });

    this.profileForm = this.fb.group({
      firstName: ['', [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(50),
        nameValidator
      ]],
      lastName: ['', [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(50),
        nameValidator
      ]],
      email: ['', [
        Validators.required,
        Validators.email,
        Validators.maxLength(100)
      ]],
      phoneNumber: ['', [
        Validators.required,
        Validators.minLength(8),
        Validators.maxLength(20),
        phoneValidator
      ]]
    });

    this.forgotPasswordForm = this.fb.group({
      email: ['', [
        Validators.required,
        Validators.email,
        Validators.maxLength(100)
      ]]
    });
  }

  private populateProfileForm(): void {
    if (this.currentUser) {
      this.profileForm.patchValue({
        firstName: this.currentUser.firstName,
        lastName: this.currentUser.lastName,
        email: this.currentUser.email,
        phoneNumber: this.currentUser.phoneNumber
      });
    }
  }

  private passwordMatchValidator(form: FormGroup): { [key: string]: boolean } | null {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');

    if (!password || !confirmPassword) {
      return null;
    }

    return password.value === confirmPassword.value ? null : { passwordMismatch: true };
  }

  setActiveTab(tab: 'login' | 'register' | 'profile' | 'forgot-password'): void {
    this.activeTab = tab;
    this.errorMessage = '';
    this.successMessage = '';
  }

  onLogin(): void {
    if (this.loginForm.invalid) {
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = '';

    const { email, password } = this.loginForm.value;

    this.authService.login(email, password).subscribe({
      next: (user) => {
        this.isSubmitting = false;
        this.currentUser = user;
        this.activeTab = 'profile';
        this.populateProfileForm();
        this.successMessage = 'Connexion réussie!';
      },
      error: (error) => {
        this.isSubmitting = false;
        this.errorMessage = 'Identifiants incorrects. Veuillez réessayer.';
        console.error('Login error:', error);
      }
    });
  }

  onRegister(): void {
    if (this.registerForm.invalid) {
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = '';

    const { firstName, lastName, email, phoneNumber, password } = this.registerForm.value;

    this.authService.register({ firstName, lastName, email, phoneNumber }, password).subscribe({
      next: (user) => {
        this.isSubmitting = false;
        this.currentUser = user;
        this.activeTab = 'profile';
        this.populateProfileForm();
        this.successMessage = 'Inscription réussie! Un email de confirmation a été envoyé.';
      },
      error: (error) => {
        this.isSubmitting = false;
        this.errorMessage = 'Une erreur est survenue lors de l\'inscription. Veuillez réessayer.';
        console.error('Register error:', error);
      }
    });
  }

  onUpdateProfile(): void {
    if (this.profileForm.invalid) {
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = '';

    const { firstName, lastName, phoneNumber } = this.profileForm.value;

    this.authService.updateUserProfile({ firstName, lastName, phoneNumber }).subscribe({
      next: (user) => {
        this.isSubmitting = false;
        this.currentUser = user;
        this.successMessage = 'Profil mis à jour avec succès!';
      },
      error: (error) => {
        this.isSubmitting = false;
        this.errorMessage = 'Une erreur est survenue lors de la mise à jour du profil.';
        console.error('Update profile error:', error);
      }
    });
  }

  onForgotPassword(): void {
    if (this.forgotPasswordForm.invalid) {
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = '';

    const { email } = this.forgotPasswordForm.value;

    // Simuler l'envoi d'un email de réinitialisation
    setTimeout(() => {
      this.isSubmitting = false;
      this.successMessage = 'Un email de réinitialisation a été envoyé à ' + email;
      this.forgotPasswordForm.reset();
    }, 1500);
  }

  onLogout(): void {
    this.authService.logout();
    this.currentUser = null;
    this.activeTab = 'login';
    this.successMessage = 'Vous avez été déconnecté avec succès.';
    this.router.navigate(['/']);
  }

  navigateToReservations(): void {
    this.router.navigate(['/account/reservations']);
  }
}
