<div class="min-h-screen bg-gray-50 py-8">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-gray-900">Interface de Test - Paiement</h1>
      <p class="text-gray-600 mt-2">Testez les différents scénarios de paiement et validez le comportement du système</p>
    </div>

    <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
      <!-- Test Configuration -->
      <div class="xl:col-span-1">
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Configuration du Test</h2>
          
          <!-- Scenario Selection -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">Scénarios prédéfinis</label>
            <div class="space-y-2">
              <div *ngFor="let scenario of testScenarios" class="flex items-center">
                <input
                  type="radio"
                  [id]="'scenario-' + scenario.name"
                  [value]="scenario.name"
                  [checked]="selectedScenario === scenario && !useCustomValues"
                  (change)="selectScenario(scenario)"
                  class="h-4 w-4 text-gold-600 focus:ring-gold-500 border-gray-300"
                >
                <label [for]="'scenario-' + scenario.name" class="ml-3 block text-sm text-gray-700">
                  <div class="font-medium">{{ scenario.name }}</div>
                  <div class="text-gray-500 text-xs">{{ scenario.description }}</div>
                  <div class="text-gold-600 text-xs">{{ scenario.amount }} TND - {{ scenario.reservationId }}</div>
                </label>
              </div>
            </div>
          </div>

          <!-- Custom Values -->
          <div class="mb-6">
            <div class="flex items-center mb-3">
              <input
                type="checkbox"
                id="use-custom"
                [(ngModel)]="useCustomValues"
                class="h-4 w-4 text-gold-600 focus:ring-gold-500 border-gray-300 rounded"
              >
              <label for="use-custom" class="ml-2 block text-sm font-medium text-gray-700">
                Utiliser des valeurs personnalisées
              </label>
            </div>
            
            <div *ngIf="useCustomValues" class="space-y-4">
              <div>
                <label for="custom-amount" class="block text-sm font-medium text-gray-700">Montant (TND)</label>
                <input
                  type="number"
                  id="custom-amount"
                  [(ngModel)]="customAmount"
                  min="1"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-gold-500 focus:border-gold-500"
                >
              </div>
              
              <div>
                <label for="custom-reservation" class="block text-sm font-medium text-gray-700">ID Réservation</label>
                <input
                  type="text"
                  id="custom-reservation"
                  [(ngModel)]="customReservationId"
                  class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-gold-500 focus:border-gold-500"
                >
              </div>
            </div>
          </div>

          <!-- Current Test Values -->
          <div class="bg-gray-50 p-4 rounded-md">
            <h3 class="text-sm font-medium text-gray-900 mb-2">Valeurs actuelles du test</h3>
            <div class="text-sm text-gray-600">
              <div>Montant: <span class="font-medium">{{ getCurrentAmount() }} TND</span></div>
              <div>Réservation: <span class="font-medium">{{ getCurrentReservationId() }}</span></div>
            </div>
          </div>
        </div>

        <!-- Service Testing -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Tests du Service</h2>
          <p class="text-sm text-gray-600 mb-4">Testez directement les méthodes du PaymentService</p>
          
          <div class="space-y-3">
            <button
              (click)="testCreatePaymentIntent()"
              class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md text-sm font-medium transition-colors"
            >
              Test createPaymentIntent
            </button>
            
            <button
              (click)="testConfirmCardPayment()"
              class="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-md text-sm font-medium transition-colors"
            >
              Test confirmCardPayment
            </button>
            
            <button
              (click)="testCreatePayPalOrder()"
              class="w-full bg-yellow-600 hover:bg-yellow-700 text-white py-2 px-4 rounded-md text-sm font-medium transition-colors"
            >
              Test createPayPalOrder
            </button>
            
            <button
              (click)="testCapturePayPalOrder()"
              class="w-full bg-purple-600 hover:bg-purple-700 text-white py-2 px-4 rounded-md text-sm font-medium transition-colors"
            >
              Test capturePayPalOrder
            </button>
          </div>
        </div>
      </div>

      <!-- Payment Form Testing -->
      <div class="xl:col-span-1">
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Test du Formulaire de Paiement</h2>
          
          <app-payment-form
            [amount]="getCurrentAmount()"
            [reservationId]="getCurrentReservationId()"
            (success)="onPaymentSuccess($event)"
            (error)="onPaymentError($event)"
          ></app-payment-form>
        </div>
      </div>

      <!-- Test Results -->
      <div class="xl:col-span-1">
        <!-- Results Actions -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Actions</h2>
          <div class="space-y-3">
            <button
              (click)="clearResults()"
              class="w-full bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-md text-sm font-medium transition-colors"
            >
              Effacer les résultats
            </button>
            
            <button
              (click)="exportResults()"
              class="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md text-sm font-medium transition-colors"
            >
              Exporter les résultats
            </button>
          </div>
        </div>

        <!-- Payment Form Results -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Résultats des Tests de Formulaire</h2>
          
          <div *ngIf="testResults.length === 0" class="text-gray-500 text-sm">
            Aucun test effectué
          </div>
          
          <div *ngIf="testResults.length > 0" class="space-y-3 max-h-64 overflow-y-auto">
            <div *ngFor="let result of testResults" 
                 class="p-3 rounded-md border"
                 [ngClass]="{
                   'bg-green-50 border-green-200': result.success,
                   'bg-red-50 border-red-200': !result.success
                 }">
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium">{{ result.scenario }}</span>
                <span class="text-xs text-gray-500">{{ result.timestamp | date:'short' }}</span>
              </div>
              
              <div *ngIf="result.success" class="text-sm text-green-700 mt-1">
                ✓ Succès - ID: {{ result.paymentId }}
              </div>
              
              <div *ngIf="!result.success" class="text-sm text-red-700 mt-1">
                ✗ Erreur: {{ result.error }}
              </div>
            </div>
          </div>
        </div>

        <!-- Service Test Results -->
        <div class="bg-white rounded-lg shadow-md p-6">
          <h2 class="text-xl font-semibold text-gray-900 mb-4">Résultats des Tests de Service</h2>
          
          <div *ngIf="serviceTestResults.length === 0" class="text-gray-500 text-sm">
            Aucun test de service effectué
          </div>
          
          <div *ngIf="serviceTestResults.length > 0" class="space-y-3 max-h-64 overflow-y-auto">
            <div *ngFor="let result of serviceTestResults" 
                 class="p-3 rounded-md border"
                 [ngClass]="{
                   'bg-green-50 border-green-200': result.success,
                   'bg-red-50 border-red-200': !result.success
                 }">
              <div class="flex items-center justify-between">
                <span class="text-sm font-medium">{{ result.method }}</span>
                <span class="text-xs text-gray-500">{{ result.timestamp | date:'short' }}</span>
              </div>
              
              <div *ngIf="result.success" class="text-sm text-green-700 mt-1">
                ✓ Succès
                <div *ngIf="result.result" class="text-xs text-gray-600 mt-1 font-mono">
                  {{ result.result | json }}
                </div>
              </div>
              
              <div *ngIf="!result.success" class="text-sm text-red-700 mt-1">
                ✗ Erreur: {{ result.error }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
