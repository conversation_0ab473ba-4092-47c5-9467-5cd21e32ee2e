<div class="min-h-screen bg-gray-50 py-8">
  <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="mb-8">
      <button
        (click)="goBack()"
        class="flex items-center text-gold-600 hover:text-gold-700 mb-4"
      >
        <span class="material-icons mr-2">arrow_back</span>
        Retour
      </button>

      <h1 class="text-3xl font-bold text-gray-900">Paiement</h1>
      <p class="text-gray-600 mt-2">Finalisez votre réservation en effectuant le paiement</p>
    </div>

    <!-- Loading State -->
    <div *ngIf="loading" class="flex justify-center items-center py-12">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-gold-600"></div>
      <span class="ml-3 text-gray-600">Chargement de votre réservation...</span>
    </div>

    <!-- Error State -->
    <div *ngIf="error && !loading" class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
      <div class="flex">
        <span class="material-icons text-red-400 mr-3">error</span>
        <div>
          <h3 class="text-sm font-medium text-red-800">Erreur</h3>
          <p class="text-sm text-red-700 mt-1">{{ error }}</p>
        </div>
      </div>
    </div>

    <!-- Payment Content -->
    <div *ngIf="reservation && !loading" class="grid grid-cols-1 lg:grid-cols-2 gap-8">
      <!-- Reservation Summary -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Résumé de la réservation</h2>

        <div class="space-y-4">
          <div class="flex justify-between">
            <span class="text-gray-600">ID de réservation:</span>
            <span class="font-medium">{{ reservation.id }}</span>
          </div>

          <div class="flex justify-between">
            <span class="text-gray-600">Nombre d'invités:</span>
            <span class="font-medium">{{ reservation.numberOfGuests }}</span>
          </div>

          <div class="flex justify-between">
            <span class="text-gray-600">Statut:</span>
            <span
              class="px-2 py-1 rounded-full text-xs font-medium"
              [ngClass]="{
                'bg-yellow-100 text-yellow-800': reservation.status === 'pending',
                'bg-green-100 text-green-800': reservation.status === 'confirmed',
                'bg-red-100 text-red-800': reservation.status === 'cancelled'
              }"
            >
              {{ reservation.status === 'pending' ? 'En attente' :
                 reservation.status === 'confirmed' ? 'Confirmée' : 'Annulée' }}
            </span>
          </div>

          <div class="border-t border-gray-200 pt-4">
            <div class="flex justify-between text-lg font-semibold">
              <span>Total à payer:</span>
              <span class="text-gold-600">{{ reservation.totalPrice }} TND</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Payment Form -->
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold text-gray-900 mb-4">Informations de paiement</h2>

        <app-payment-form
          [amount]="reservation.totalPrice"
          [reservationId]="reservation.id"
          (success)="onPaymentSuccess($event)"
          (error)="onPaymentError($event)"
        ></app-payment-form>
      </div>
    </div>

    <!-- Security Notice -->
    <div *ngIf="reservation && !loading" class="mt-8 bg-blue-50 border border-blue-200 rounded-md p-4">
      <div class="flex">
        <span class="material-icons text-blue-400 mr-3">security</span>
        <div>
          <h3 class="text-sm font-medium text-blue-800">Paiement sécurisé</h3>
          <p class="text-sm text-blue-700 mt-1">
            Vos informations de paiement sont protégées par un cryptage SSL de niveau bancaire.
            Nous ne stockons jamais vos données de carte de crédit.
          </p>
        </div>
      </div>
    </div>
  </div>
</div>
