export interface Event {
  id: string;
  title: string;
  description: string;
  date: string;
  time: string;
  imageUrl: string;
  images?: string[];
  iconUrl?: string | null;
  restaurantId: string;
  maxCapacity: number;
  pricePerPerson: number;
  isActive?: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Restaurant {
  id: string;
  name: string;
  description: string;
  imageUrl: string;
  createdAt: string;
  updatedAt: string;
}
