import { ComponentFixture, TestBed } from '@angular/core/testing';
import { of, throwError } from 'rxjs';
import { DebugElement } from '@angular/core';
import { By } from '@angular/platform-browser';

import { PaymentFormComponent } from './payment-form.component';
import { PaymentService } from '../../services/payment.service';

describe('PaymentFormComponent', () => {
  let component: PaymentFormComponent;
  let fixture: ComponentFixture<PaymentFormComponent>;
  let mockPaymentService: jasmine.SpyObj<PaymentService>;

  beforeEach(async () => {
    const spy = jasmine.createSpyObj('PaymentService', [
      'createPaymentIntent',
      'confirmCardPayment',
      'createPayPalOrder',
      'capturePayPalOrder'
    ]);

    await TestBed.configureTestingModule({
      imports: [PaymentFormComponent],
      providers: [
        { provide: PaymentService, useValue: spy }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PaymentFormComponent);
    component = fixture.componentInstance;
    mockPaymentService = TestBed.inject(PaymentService) as jasmine.SpyObj<PaymentService>;

    // Set required inputs
    component.amount = 100;
    component.reservationId = 'test-reservation-123';

    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should initialize with default values', () => {
    expect(component.paymentMethod).toBe('card');
    expect(component.isProcessing).toBe(false);
    expect(component.cardNumber).toBe('');
    expect(component.cardExpiry).toBe('');
    expect(component.cardCvc).toBe('');
    expect(component.cardName).toBe('');
  });

  it('should display the correct amount', () => {
    const amountElement = fixture.debugElement.query(By.css('.text-2xl'));
    expect(amountElement.nativeElement.textContent).toContain('100 TND');
  });

  describe('Payment Method Selection', () => {
    it('should change payment method to PayPal', () => {
      component.onPaymentMethodChange('paypal');
      expect(component.paymentMethod).toBe('paypal');
    });

    it('should change payment method to card', () => {
      component.paymentMethod = 'paypal';
      component.onPaymentMethodChange('card');
      expect(component.paymentMethod).toBe('card');
    });

    it('should show card form when card method is selected', () => {
      component.paymentMethod = 'card';
      fixture.detectChanges();

      const cardForm = fixture.debugElement.query(By.css('input[placeholder="Numéro de carte"]'));
      expect(cardForm).toBeTruthy();
    });

    it('should show PayPal form when PayPal method is selected', () => {
      component.paymentMethod = 'paypal';
      fixture.detectChanges();

      const paypalInfo = fixture.debugElement.query(By.css('.bg-blue-50'));
      expect(paypalInfo).toBeTruthy();
    });
  });

  describe('Card Payment Validation', () => {
    it('should emit error for empty card fields', () => {
      spyOn(component.error, 'emit');

      component.cardNumber = '';
      component.cardExpiry = '';
      component.cardCvc = '';
      component.cardName = '';

      component.onSubmitCardPayment();

      expect(component.error.emit).toHaveBeenCalledWith('Veuillez remplir tous les champs de la carte');
    });

    it('should not process payment with invalid card data', () => {
      component.cardNumber = '';
      component.onSubmitCardPayment();

      expect(mockPaymentService.createPaymentIntent).not.toHaveBeenCalled();
    });

    it('should validate all required card fields', () => {
      spyOn(component.error, 'emit');

      // Test missing card number
      component.cardNumber = '';
      component.cardExpiry = '12/25';
      component.cardCvc = '123';
      component.cardName = 'John Doe';
      component.onSubmitCardPayment();
      expect(component.error.emit).toHaveBeenCalled();

      // Reset spy
      (component.error.emit as jasmine.Spy).calls.reset();

      // Test missing expiry
      component.cardNumber = '****************';
      component.cardExpiry = '';
      component.onSubmitCardPayment();
      expect(component.error.emit).toHaveBeenCalled();
    });
  });

  describe('Card Payment Processing', () => {
    beforeEach(() => {
      // Set valid card data
      component.cardNumber = '****************';
      component.cardExpiry = '12/25';
      component.cardCvc = '123';
      component.cardName = 'John Doe';
    });

    it('should process successful card payment', () => {
      const mockPaymentIntent = {
        clientSecret: 'pi_test_secret',
        paymentIntentId: 'pi_test_123'
      };
      const mockConfirmation = { success: true };

      mockPaymentService.createPaymentIntent.and.returnValue(of(mockPaymentIntent));
      mockPaymentService.confirmCardPayment.and.returnValue(of(mockConfirmation));

      spyOn(component.success, 'emit');

      component.onSubmitCardPayment();

      expect(component.isProcessing).toBe(false);
      expect(component.success.emit).toHaveBeenCalledWith('pi_test_123');
    });

    it('should handle payment intent creation error', () => {
      mockPaymentService.createPaymentIntent.and.returnValue(
        throwError(() => new Error('Payment intent creation failed'))
      );

      spyOn(component.error, 'emit');

      component.onSubmitCardPayment();

      expect(component.isProcessing).toBe(false);
      expect(component.error.emit).toHaveBeenCalledWith(
        'Une erreur est survenue lors de la création de l\'intention de paiement'
      );
    });

    it('should handle payment confirmation error', () => {
      const mockPaymentIntent = {
        clientSecret: 'pi_test_secret',
        paymentIntentId: 'pi_test_123'
      };
      const mockConfirmation = { success: false, error: 'Card declined' };

      mockPaymentService.createPaymentIntent.and.returnValue(of(mockPaymentIntent));
      mockPaymentService.confirmCardPayment.and.returnValue(of(mockConfirmation));

      spyOn(component.error, 'emit');

      component.onSubmitCardPayment();

      expect(component.error.emit).toHaveBeenCalledWith('Card declined');
    });

    it('should set processing state during payment', () => {
      const mockPaymentIntent = {
        clientSecret: 'pi_test_secret',
        paymentIntentId: 'pi_test_123'
      };

      mockPaymentService.createPaymentIntent.and.returnValue(of(mockPaymentIntent));
      mockPaymentService.confirmCardPayment.and.returnValue(of({ success: true }));

      expect(component.isProcessing).toBe(false);

      component.onSubmitCardPayment();

      // Processing should be false after completion
      expect(component.isProcessing).toBe(false);
    });
  });

  describe('PayPal Payment Processing', () => {
    it('should process successful PayPal payment', (done) => {
      const mockOrder = {
        orderId: 'order_test_123',
        approvalUrl: 'https://paypal.com/test'
      };
      const mockCapture = { success: true };

      mockPaymentService.createPayPalOrder.and.returnValue(of(mockOrder));
      mockPaymentService.capturePayPalOrder.and.returnValue(of(mockCapture));

      spyOn(component.success, 'emit');

      component.onSubmitPayPalPayment();

      // Wait for the setTimeout in the component
      setTimeout(() => {
        expect(component.success.emit).toHaveBeenCalledWith('order_test_123');
        expect(component.isProcessing).toBe(false);
        done();
      }, 2100);
    });

    it('should handle PayPal order creation error', () => {
      mockPaymentService.createPayPalOrder.and.returnValue(
        throwError(() => new Error('PayPal order creation failed'))
      );

      spyOn(component.error, 'emit');

      component.onSubmitPayPalPayment();

      expect(component.error.emit).toHaveBeenCalledWith(
        'Une erreur est survenue lors de la création de la commande PayPal'
      );
    });

    it('should handle PayPal capture error', (done) => {
      const mockOrder = {
        orderId: 'order_test_123',
        approvalUrl: 'https://paypal.com/test'
      };
      const mockCapture = { success: false, error: 'Insufficient funds' };

      mockPaymentService.createPayPalOrder.and.returnValue(of(mockOrder));
      mockPaymentService.capturePayPalOrder.and.returnValue(of(mockCapture));

      spyOn(component.error, 'emit');

      component.onSubmitPayPalPayment();

      setTimeout(() => {
        expect(component.error.emit).toHaveBeenCalledWith('Insufficient funds');
        done();
      }, 2100);
    });
  });

  describe('Event Emissions', () => {
    it('should emit success event with payment ID', () => {
      spyOn(component.success, 'emit');

      const testPaymentId = 'test_payment_123';
      component.success.emit(testPaymentId);

      expect(component.success.emit).toHaveBeenCalledWith(testPaymentId);
    });

    it('should emit error event with error message', () => {
      spyOn(component.error, 'emit');

      const testError = 'Test error message';
      component.error.emit(testError);

      expect(component.error.emit).toHaveBeenCalledWith(testError);
    });
  });
});
