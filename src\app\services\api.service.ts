import { Injectable } from '@angular/core';
import { HttpClient, HttpErrorResponse, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';

@Injectable({
  providedIn: 'root'
})
export class ApiService {
  // Strapi default URL - change as needed
  private apiUrl = 'http://localhost:1337/api';

  // Fallback to mock data if API is not available
  private useLocalMockData = true;

  constructor(private http: HttpClient) {}

  /**
   * Get JWT token from local storage
   */
  private getToken(): string | null {
    return localStorage.getItem('auth_token');
  }

  /**
   * Create headers for API requests
   */
  private createHeaders(): HttpHeaders {
    const token = this.getToken();
    let headers = new HttpHeaders({
      'Content-Type': 'application/json'
    });

    if (token) {
      headers = headers.set('Authorization', `Bearer ${token}`);
    }

    return headers;
  }

  /**
   * Handle API errors
   */
  private handleError(error: HttpErrorResponse) {
    let errorMessage = 'Une erreur est survenue';

    if (error.error instanceof ErrorEvent) {
      // Client-side error
      errorMessage = `Erreur: ${error.error.message}`;
    } else {
      // Server-side error
      if (error.status === 0) {
        errorMessage = 'Impossible de se connecter au serveur. Veuillez vérifier votre connexion internet.';
      } else if (error.status === 401) {
        errorMessage = 'Non autorisé. Veuillez vous connecter.';
      } else if (error.status === 403) {
        errorMessage = 'Accès refusé.';
      } else if (error.status === 404) {
        errorMessage = 'Ressource non trouvée.';
      } else if (error.status >= 500) {
        errorMessage = 'Erreur serveur. Veuillez réessayer plus tard.';
      }

      // Try to get more specific error message from Strapi
      if (error.error) {
        // Strapi v4 error format
        if (error.error.error) {
          if (error.error.error.message) {
            errorMessage = error.error.error.message;
          } else if (error.error.error.details && error.error.error.details.errors) {
            errorMessage = error.error.error.details.errors
              .map((err: any) => err.message)
              .join(', ');
          }
        }
        // Strapi v3 error format
        else if (error.error.message) {
          errorMessage = error.error.message;
        } else if (Array.isArray(error.error) && error.error.length > 0 && error.error[0].messages) {
          errorMessage = error.error[0].messages.map((msg: any) => msg.message).join(', ');
        }
      }
    }

    console.error('API Error:', error);
    return throwError(() => new Error(errorMessage));
  }

  /**
   * GET request
   */
  get<T>(endpoint: string, params: any = {}): Observable<T> {
    const headers = this.createHeaders();
    const url = `${this.apiUrl}/${endpoint}`;

    return this.http.get<T>(url, { headers, params }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * POST request
   */
  post<T>(endpoint: string, data: any): Observable<T> {
    const headers = this.createHeaders();
    const url = `${this.apiUrl}/${endpoint}`;

    return this.http.post<T>(url, data, { headers }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * PUT request
   */
  put<T>(endpoint: string, data: any): Observable<T> {
    const headers = this.createHeaders();
    const url = `${this.apiUrl}/${endpoint}`;

    return this.http.put<T>(url, data, { headers }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * DELETE request
   */
  delete<T>(endpoint: string): Observable<T> {
    const headers = this.createHeaders();
    const url = `${this.apiUrl}/${endpoint}`;

    return this.http.delete<T>(url, { headers }).pipe(
      catchError(this.handleError)
    );
  }

  /**
   * Upload file to Strapi
   */
  uploadFile(file: File): Observable<any> {
    const formData = new FormData();
    formData.append('files', file);

    const headers = new HttpHeaders({
      'Authorization': `Bearer ${this.getToken()}`
    });

    return this.http.post<any>(`${this.apiUrl}/upload`, formData, { headers }).pipe(
      catchError(this.handleError)
    );
  }
}
