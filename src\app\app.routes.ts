import { Routes } from '@angular/router';

import { HomeComponent } from './pages/home/<USER>';
import { EventsComponent } from './pages/events/events.component';
import { EventDetailComponent } from './pages/event-detail/event-detail.component';
import { ReservationComponent } from './pages/reservation/reservation.component';
import { PaymentComponent } from './pages/payment/payment.component';
import { SuccessComponent } from './pages/success/success.component';
import { AccountComponent } from './pages/account/account.component';
import { ReservationsComponent } from './pages/reservations/reservations.component';
import { ContactComponent } from './pages/contact/contact.component';
import { EventTestComponent } from './components/event-test/event-test.component';
import { PaymentTestComponent } from './components/payment-test/payment-test.component';

export const routes: Routes = [
  { path: '', component: HomeComponent },
  { path: 'events', component: EventsComponent },
  { path: 'events/:id', component: EventDetailComponent },
  { path: 'reservation/:eventId', component: ReservationComponent },
  { path: 'payment/:reservationId', component: PaymentComponent },
  { path: 'success/:reservationId', component: SuccessComponent },
  { path: 'account', component: AccountComponent },
  { path: 'account/reservations', component: ReservationsComponent },
  { path: 'contact', component: ContactComponent },
  { path: 'test-events', component: EventTestComponent },
  { path: 'test-payment', component: PaymentTestComponent },
  { path: '**', redirectTo: '' }
];
