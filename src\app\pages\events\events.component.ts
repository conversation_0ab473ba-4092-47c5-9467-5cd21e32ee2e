import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { EventService } from '../../services/event.service';
import { Event } from '../../models/event.model';
import { EventCardComponent } from '../../components/event-card/event-card.component';
import { EventFilterComponent } from '../../components/event-filter/event-filter.component';

@Component({
  selector: 'app-events',
  standalone: true,
  imports: [CommonModule, EventCardComponent, EventFilterComponent],
  templateUrl: './events.component.html',
  styleUrl: './events.component.scss'
})
export class EventsComponent implements OnInit {
  events: Event[] = [];
  filteredEvents: Event[] = [];
  isLoading = true;
  errorMessage = '';

  constructor(private eventService: EventService) {}

  ngOnInit(): void {
    this.loadEvents();
  }

  loadEvents(): void {
    this.isLoading = true;
    this.errorMessage = '';
    this.eventService.getAllEvents().subscribe({
      next: (events) => {
        this.events = events;
        this.filteredEvents = events;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error loading events:', error);
        this.errorMessage = 'Une erreur est survenue lors du chargement des événements. Veuillez réessayer plus tard.';
        this.isLoading = false;
      }
    });
  }

  onFilterChange(filters: { search: string; date: string }): void {
    this.isLoading = true;
    this.errorMessage = '';
    this.eventService.searchEvents(filters.search, filters.date).subscribe({
      next: (events) => {
        this.filteredEvents = events;
        this.isLoading = false;
      },
      error: (error) => {
        console.error('Error filtering events:', error);
        this.errorMessage = 'Une erreur est survenue lors de la recherche des événements. Veuillez réessayer plus tard.';
        this.isLoading = false;
      }
    });
  }
}
