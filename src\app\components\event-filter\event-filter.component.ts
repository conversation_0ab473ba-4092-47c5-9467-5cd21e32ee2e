import { Component, EventEmitter, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

interface FilterCriteria {
  search: string;
  date: string;
}

@Component({
  selector: 'app-event-filter',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './event-filter.component.html',
  styleUrl: './event-filter.component.scss'
})
export class EventFilterComponent {
  @Output() filterChange = new EventEmitter<FilterCriteria>();

  search = '';
  date = '';

  onSearchChange(): void {
    this.emitFilterChange();
  }

  onDateChange(): void {
    this.emitFilterChange();
  }

  private emitFilterChange(): void {
    this.filterChange.emit({
      search: this.search,
      date: this.date
    });
  }
}
