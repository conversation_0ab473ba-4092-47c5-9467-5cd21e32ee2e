import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { ApiService } from '../../services/api.service';

@Component({
  selector: 'app-contact',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './contact.component.html',
  styleUrl: './contact.component.scss'
})
export class ContactComponent implements OnInit {
  contactForm!: FormGroup;
  isSubmitting = false;
  submitSuccess = false;
  errorMessage = '';

  constructor(
    private fb: FormBuilder,
    private apiService: ApiService
  ) {}

  ngOnInit(): void {
    this.initForm();
  }

  private initForm(): void {
    // Create name validator function
    const nameValidator = (control: { value: string }) => {
      const valid = /^[a-zA-ZÀ-ÿ\s'-]+$/.test(control.value);
      return valid ? null : { invalidName: true };
    };

    // Create phone validator function
    const phoneValidator = (control: { value: string }) => {
      const valid = /^[0-9+\s()-]{8,20}$/.test(control.value);
      return valid ? null : { invalidPhone: true };
    };

    this.contactForm = this.fb.group({
      firstName: ['', [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(50),
        nameValidator
      ]],
      lastName: ['', [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(50),
        nameValidator
      ]],
      email: ['', [
        Validators.required,
        Validators.email,
        Validators.maxLength(100)
      ]],
      phone: ['', [
        Validators.minLength(8),
        Validators.maxLength(20),
        phoneValidator
      ]],
      subject: ['', [
        Validators.required,
        Validators.minLength(5),
        Validators.maxLength(100)
      ]],
      message: ['', [
        Validators.required,
        Validators.minLength(10),
        Validators.maxLength(1000)
      ]]
    });
  }

  onSubmit(): void {
    if (this.contactForm.invalid) {
      // Mark all fields as touched to trigger validation messages
      Object.keys(this.contactForm.controls).forEach(key => {
        this.contactForm.get(key)?.markAsTouched();
      });
      return;
    }

    this.isSubmitting = true;
    this.errorMessage = '';


    const formData = {
      ...this.contactForm.value,
      statusC: 'new'
    };
    
    

    const contactData = {
      data: formData
    };

    this.apiService.post('contacts', contactData).subscribe({
      next: () => {
        this.isSubmitting = false;
        this.submitSuccess = true;
        this.contactForm.reset();
      },
      error: (error) => {
        this.isSubmitting = false;
        console.error('Error submitting contact form:', error);
        this.errorMessage = 'Une erreur est survenue lors de l\'envoi du message. Veuillez réessayer plus tard.';
      }
    });
  }
}
