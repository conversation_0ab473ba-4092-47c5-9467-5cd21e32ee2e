import { TestBed } from '@angular/core/testing';
import { of } from 'rxjs';

import { PaymentService } from './payment.service';
import { ReservationService } from './reservation.service';

describe('PaymentService', () => {
  let service: PaymentService;
  let mockReservationService: jasmine.SpyObj<ReservationService>;

  beforeEach(() => {
    const spy = jasmine.createSpyObj('ReservationService', ['updateReservationPayment']);

    TestBed.configureTestingModule({
      providers: [
        PaymentService,
        { provide: ReservationService, useValue: spy }
      ]
    });

    service = TestBed.inject(PaymentService);
    mockReservationService = TestBed.inject(ReservationService) as jasmine.SpyObj<ReservationService>;
  });

  it('should be created', () => {
    expect(service).toBeTruthy();
  });

  describe('createPaymentIntent', () => {
    it('should create a payment intent with correct amount and reservation ID', (done) => {
      const amount = 100;
      const reservationId = 'test-reservation-123';

      service.createPaymentIntent(amount, reservationId).subscribe(result => {
        expect(result.clientSecret).toBeDefined();
        expect(result.paymentIntentId).toBeDefined();
        expect(result.clientSecret).toContain('secret');
        expect(result.paymentIntentId).toContain('pi_');
        done();
      });
    });

    it('should generate unique payment intent IDs', (done) => {
      const amount = 100;
      const reservationId = 'test-reservation-123';
      const results: string[] = [];

      service.createPaymentIntent(amount, reservationId).subscribe(result1 => {
        results.push(result1.paymentIntentId);

        service.createPaymentIntent(amount, reservationId).subscribe(result2 => {
          results.push(result2.paymentIntentId);

          expect(results[0]).not.toEqual(results[1]);
          done();
        });
      });
    });
  });

  describe('confirmCardPayment', () => {
    it('should confirm payment for existing payment intent', (done) => {
      const amount = 100;
      const reservationId = 'test-reservation-123';

      service.createPaymentIntent(amount, reservationId).subscribe(({ paymentIntentId }) => {
        service.confirmCardPayment(paymentIntentId).subscribe(result => {
          expect(result.success).toBe(true);
          expect(result.error).toBeUndefined();
          done();
        });
      });
    });

    it('should return error for non-existent payment intent', (done) => {
      const nonExistentId = 'pi_nonexistent';

      service.confirmCardPayment(nonExistentId).subscribe(result => {
        expect(result.success).toBe(false);
        expect(result.error).toBe('Payment intent not found');
        done();
      });
    });
  });

  describe('createPayPalOrder', () => {
    it('should create PayPal order with correct amount and reservation ID', (done) => {
      const amount = 150;
      const reservationId = 'test-reservation-456';

      service.createPayPalOrder(amount, reservationId).subscribe(result => {
        expect(result.orderId).toBeDefined();
        expect(result.approvalUrl).toBeDefined();
        expect(result.orderId).toContain('order_');
        expect(result.approvalUrl).toContain('paypal.com');
        done();
      });
    });

    it('should generate unique order IDs', (done) => {
      const amount = 150;
      const reservationId = 'test-reservation-456';
      const results: string[] = [];

      service.createPayPalOrder(amount, reservationId).subscribe(result1 => {
        results.push(result1.orderId);

        service.createPayPalOrder(amount, reservationId).subscribe(result2 => {
          results.push(result2.orderId);

          expect(results[0]).not.toEqual(results[1]);
          done();
        });
      });
    });
  });

  describe('capturePayPalOrder', () => {
    it('should capture PayPal order successfully', (done) => {
      const orderId = 'order_test123';

      service.capturePayPalOrder(orderId).subscribe(result => {
        expect(result.success).toBe(true);
        expect(result.error).toBeUndefined();
        done();
      });
    });
  });

  describe('Payment Intent Management', () => {
    it('should store and retrieve payment intents correctly', (done) => {
      const amount = 200;
      const reservationId = 'test-reservation-789';

      service.createPaymentIntent(amount, reservationId).subscribe(({ paymentIntentId }) => {
        // Verify the payment intent was stored by trying to confirm it
        service.confirmCardPayment(paymentIntentId).subscribe(result => {
          expect(result.success).toBe(true);
          done();
        });
      });
    });

    it('should update payment intent status after confirmation', (done) => {
      const amount = 300;
      const reservationId = 'test-reservation-101';

      service.createPaymentIntent(amount, reservationId).subscribe(({ paymentIntentId }) => {
        service.confirmCardPayment(paymentIntentId).subscribe(result => {
          expect(result.success).toBe(true);

          // Try to confirm again - should still work since we're simulating
          service.confirmCardPayment(paymentIntentId).subscribe(secondResult => {
            expect(secondResult.success).toBe(true);
            done();
          });
        });
      });
    });
  });
});
