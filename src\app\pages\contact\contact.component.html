<div class="container mx-auto px-4 py-12">
  <div class="max-w-4xl mx-auto">
    <h1 class="text-3xl md:text-4xl font-bold text-center mb-8">Contactez-nous</h1>
    
    <div class="bg-white shadow-lg rounded-lg overflow-hidden mb-10">
      <div class="p-6 md:p-8">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- Contact Information -->
          <div class="space-y-6">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Nos Coordonnées</h2>
            
            <div class="flex items-start space-x-4">
              <div class="text-gold-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <div>
                <h3 class="font-medium text-gray-800">Adresse</h3>
                <p class="text-gray-600 mt-1">Boite Postale 697, 2070 La Marsa</p>
                <p class="text-gray-600">Les Côtes de Carthage</p>
              </div>
            </div>
            
            <div class="flex items-start space-x-4">
              <div class="text-gold-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
              </div>
              <div>
                <h3 class="font-medium text-gray-800">Téléphone</h3>
                <p class="text-gray-600 mt-1">+216 71 910 101</p>
              </div>
            </div>
            
            <div class="flex items-start space-x-4">
              <div class="text-gold-600">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
              </div>
              <div>
                <h3 class="font-medium text-gray-800">Email</h3>
                <p class="text-gray-600 mt-1">info-tunis&#64;theresidence.com</p>
              </div>
            </div>
            
            <div class="mt-8">
              <h3 class="font-medium text-gray-800 mb-2">Heures d'ouverture</h3>
              <p class="text-gray-600">Lundi - Vendredi: 9h00 - 18h00</p>
              <p class="text-gray-600">Samedi: 10h00 - 15h00</p>
              <p class="text-gray-600">Dimanche: Fermé</p>
            </div>
          </div>
          
          <!-- Contact Form -->
          <div>
            <h2 class="text-2xl font-semibold text-gray-800 mb-4">Envoyez-nous un message</h2>
            
            <div *ngIf="submitSuccess" class="mb-6 p-4 bg-green-100 text-green-700 rounded-md">
              Votre message a été envoyé avec succès. Nous vous répondrons dans les plus brefs délais.
            </div>
            
            <div *ngIf="errorMessage" class="mb-6 p-4 bg-red-100 text-red-700 rounded-md">
              {{ errorMessage }}
            </div>
            
            <form *ngIf="!submitSuccess" [formGroup]="contactForm" (ngSubmit)="onSubmit()" class="space-y-4">
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label for="firstName" class="block text-sm font-medium text-gray-700 mb-1">
                    Prénom *
                  </label>
                  <input
                    id="firstName"
                    type="text"
                    formControlName="firstName"
                    class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
                    [ngClass]="{'border-red-500': contactForm.get('firstName')?.invalid && contactForm.get('firstName')?.touched, 'border-gray-300': !(contactForm.get('firstName')?.invalid && contactForm.get('firstName')?.touched)}"
                  />
                  <div *ngIf="contactForm.get('firstName')?.invalid && contactForm.get('firstName')?.touched" class="mt-1 text-sm text-red-600">
                    <div *ngIf="contactForm.get('firstName')?.errors?.['required']">Le prénom est requis.</div>
                    <div *ngIf="contactForm.get('firstName')?.errors?.['minlength']">Le prénom doit contenir au moins 2 caractères.</div>
                    <div *ngIf="contactForm.get('firstName')?.errors?.['invalidName']">Le prénom contient des caractères non autorisés.</div>
                  </div>
                </div>
                
                <div>
                  <label for="lastName" class="block text-sm font-medium text-gray-700 mb-1">
                    Nom *
                  </label>
                  <input
                    id="lastName"
                    type="text"
                    formControlName="lastName"
                    class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
                    [ngClass]="{'border-red-500': contactForm.get('lastName')?.invalid && contactForm.get('lastName')?.touched, 'border-gray-300': !(contactForm.get('lastName')?.invalid && contactForm.get('lastName')?.touched)}"
                  />
                  <div *ngIf="contactForm.get('lastName')?.invalid && contactForm.get('lastName')?.touched" class="mt-1 text-sm text-red-600">
                    <div *ngIf="contactForm.get('lastName')?.errors?.['required']">Le nom est requis.</div>
                    <div *ngIf="contactForm.get('lastName')?.errors?.['minlength']">Le nom doit contenir au moins 2 caractères.</div>
                    <div *ngIf="contactForm.get('lastName')?.errors?.['invalidName']">Le nom contient des caractères non autorisés.</div>
                  </div>
                </div>
              </div>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                    Email *
                  </label>
                  <input
                    id="email"
                    type="email"
                    formControlName="email"
                    class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
                    [ngClass]="{'border-red-500': contactForm.get('email')?.invalid && contactForm.get('email')?.touched, 'border-gray-300': !(contactForm.get('email')?.invalid && contactForm.get('email')?.touched)}"
                  />
                  <div *ngIf="contactForm.get('email')?.invalid && contactForm.get('email')?.touched" class="mt-1 text-sm text-red-600">
                    <div *ngIf="contactForm.get('email')?.errors?.['required']">L'email est requis.</div>
                    <div *ngIf="contactForm.get('email')?.errors?.['email']">Veuillez entrer une adresse email valide.</div>
                  </div>
                </div>
                
                <div>
                  <label for="phone" class="block text-sm font-medium text-gray-700 mb-1">
                    Téléphone
                  </label>
                  <input
                    id="phone"
                    type="tel"
                    formControlName="phone"
                    class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
                    [ngClass]="{'border-red-500': contactForm.get('phone')?.invalid && contactForm.get('phone')?.touched, 'border-gray-300': !(contactForm.get('phone')?.invalid && contactForm.get('phone')?.touched)}"
                  />
                  <div *ngIf="contactForm.get('phone')?.invalid && contactForm.get('phone')?.touched" class="mt-1 text-sm text-red-600">
                    <div *ngIf="contactForm.get('phone')?.errors?.['invalidPhone']">Veuillez entrer un numéro de téléphone valide.</div>
                  </div>
                </div>
              </div>
              
              <div>
                <label for="subject" class="block text-sm font-medium text-gray-700 mb-1">
                  Sujet *
                </label>
                <input
                  id="subject"
                  type="text"
                  formControlName="subject"
                  class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
                  [ngClass]="{'border-red-500': contactForm.get('subject')?.invalid && contactForm.get('subject')?.touched, 'border-gray-300': !(contactForm.get('subject')?.invalid && contactForm.get('subject')?.touched)}"
                />
                <div *ngIf="contactForm.get('subject')?.invalid && contactForm.get('subject')?.touched" class="mt-1 text-sm text-red-600">
                  <div *ngIf="contactForm.get('subject')?.errors?.['required']">Le sujet est requis.</div>
                  <div *ngIf="contactForm.get('subject')?.errors?.['minlength']">Le sujet doit contenir au moins 5 caractères.</div>
                </div>
              </div>
              
              <div>
                <label for="message" class="block text-sm font-medium text-gray-700 mb-1">
                  Message *
                </label>
                <textarea
                  id="message"
                  formControlName="message"
                  rows="5"
                  class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
                  [ngClass]="{'border-red-500': contactForm.get('message')?.invalid && contactForm.get('message')?.touched, 'border-gray-300': !(contactForm.get('message')?.invalid && contactForm.get('message')?.touched)}"
                ></textarea>
                <div *ngIf="contactForm.get('message')?.invalid && contactForm.get('message')?.touched" class="mt-1 text-sm text-red-600">
                  <div *ngIf="contactForm.get('message')?.errors?.['required']">Le message est requis.</div>
                  <div *ngIf="contactForm.get('message')?.errors?.['minlength']">Le message doit contenir au moins 10 caractères.</div>
                </div>
              </div>
              
              <div class="flex justify-end">
                <button type="submit" class="bg-gold-600 text-white py-2 px-4 rounded-md hover:bg-gold-700 focus:outline-none focus:ring-2 focus:ring-gold-500">
                  Envoyer
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
