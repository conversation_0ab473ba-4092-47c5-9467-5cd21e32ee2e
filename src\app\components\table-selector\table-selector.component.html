<div class="w-full">
  <h3 class="text-lg font-semibold mb-4">Sélectionnez une table</h3>
  <div class="relative w-full h-96 border border-gray-300 rounded-lg bg-gray-100 overflow-hidden">
    <!-- Restaurant layout background -->
    <div class="absolute inset-0">
      <div 
        *ngFor="let table of tables" 
        class="absolute rounded-full flex flex-col items-center justify-center transition-all"
        [ngClass]="getTableColor(getTableStatus(table))"
        [style.left.%]="table.positionX"
        [style.top.%]="table.positionY"
        [style.width.px]="getTableSize(table.capacity)"
        [style.height.px]="getTableSize(table.capacity)"
        [style.transform]="'translate(-50%, -50%)'"
        (mouseenter)="onTableHover(table)"
        (mouseleave)="onTableLeave()"
        (click)="onTableClick(table)"
      >
        <span class="text-white font-medium">{{ table.tableNumber }}</span>
        <span class="text-white text-xs">{{ getCapacityLabel(table.capacity) }}</span>
      </div>
    </div>
  </div>
  
  <div *ngIf="tables.length === 0" class="text-center p-4 text-gray-500">
    Aucune table disponible
  </div>
</div>

