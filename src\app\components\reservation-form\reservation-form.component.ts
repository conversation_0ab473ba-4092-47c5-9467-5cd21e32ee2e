import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { Event } from '../../models/event.model';
import { Table } from '../../models/table.model';
import { ReservationFormData } from '../../models/reservation.model';

@Component({
  selector: 'app-reservation-form',
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule],
  templateUrl: './reservation-form.component.html',
  styleUrl: './reservation-form.component.scss'
})
export class ReservationFormComponent implements OnInit {
  @Input() event!: Event;
  @Input() selectedTableId!: string;
  @Input() selectedTable?: Table;
  @Output() formSubmit = new EventEmitter<ReservationFormData>();

  reservationForm!: FormGroup;
  isSubmitting = false;
  totalPrice = 0;

  constructor(private fb: FormBuilder) {}

  ngOnInit(): void {
    this.initForm();
    this.updateTotalPrice();

    // Mettre à jour le prix total lorsque le nombre de personnes change
    this.reservationForm.get('numberOfGuests')?.valueChanges.subscribe(() => {
      this.updateTotalPrice();
    });
  }

  private initForm(): void {
    // Custom validators
    const nameValidator = Validators.pattern(/^[a-zA-ZÀ-ÿ\s'-]+$/);
    const phoneValidator = Validators.pattern(/^[+]?[(]?[0-9]{1,4}[)]?[-\s.]?[0-9]{1,4}[-\s.]?[0-9]{1,9}$/);

    this.reservationForm = this.fb.group({
      firstName: ['', [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(50),
        nameValidator
      ]],
      lastName: ['', [
        Validators.required,
        Validators.minLength(2),
        Validators.maxLength(50),
        nameValidator
      ]],
      email: ['', [
        Validators.required,
        Validators.email,
        Validators.maxLength(100)
      ]],
      phone: ['', [
        Validators.required,
        Validators.minLength(8),
        Validators.maxLength(20),
        phoneValidator
      ]],
      numberOfGuests: [
        1,
        [
          Validators.required,
          Validators.min(1),
          Validators.max(this.selectedTable?.capacity || 10)
        ]
      ],
      specialRequests: ['', [Validators.maxLength(500)]],
    });
  }

  private updateTotalPrice(): void {
    const numberOfGuests = this.reservationForm.get('numberOfGuests')?.value || 1;
    this.totalPrice = this.event.pricePerPerson * numberOfGuests;
  }

  onSubmit(): void {
    if (this.reservationForm.invalid) {
      return;
    }

    this.isSubmitting = true;

    try {
      this.formSubmit.emit(this.reservationForm.value);
    } catch (error) {
      console.error('Error submitting reservation:', error);
    } finally {
      this.isSubmitting = false;
    }
  }

  get maxGuests(): number {
    return this.selectedTable?.capacity || 10;
  }

  get guestOptions(): number[] {
    return Array.from({ length: this.maxGuests }, (_, i) => i + 1);
  }
}
