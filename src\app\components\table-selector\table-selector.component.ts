import { Component, EventEmitter, Input, Output } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Table } from '../../models/table.model';

@Component({
  selector: 'app-table-selector',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './table-selector.component.html',
  styleUrl: './table-selector.component.scss'
})
export class TableSelectorComponent {
  @Input() tables: Table[] = [];
  @Input() selectedTableId?: string;
  @Output() tableSelect = new EventEmitter<string>();

  hoveredTableId: string | null = null;

  /**
   * Calculate table size based on capacity
   */
  getTableSize(capacity: number): number {
    // Scale table size based on capacity
    const baseSize = 50;
    
    if (capacity <= 2) return baseSize * 0.8;
    if (capacity <= 4) return baseSize;
    if (capacity <= 6) return baseSize * 1.2;
    return baseSize * 1.4; // For 8+ people
  }

  /**
   * Get table status (available, unavailable, selected, hovered)
   */
  getTableStatus(table: Table): string {
    if (!table.isAvailable) return 'unavailable';
    if (this.selectedTableId === table.id) return 'selected';
    if (this.hoveredTableId === table.id) return 'hovered';
    return 'available';
  }

  /**
   * Get capacity label
   */
  getCapacityLabel(capacity: number): string {
    return capacity === 1 ? '1 personne' : `${capacity} personnes`;
  }

  getTableColor(status: string): string {
    switch (status) {
      case 'unavailable':
        return 'bg-gray-400 cursor-not-allowed';
      case 'selected':
        return 'bg-gold-600 border-2 border-gold-800';
      case 'hovered':
        return 'bg-gold-400';
      case 'available':
        return 'bg-green-500 hover:bg-gold-400 cursor-pointer';
      default:
        return 'bg-green-500';
    }
  }

  onTableHover(table: Table): void {
    if (table.isAvailable) {
      this.hoveredTableId = table.id;
    }
  }

  onTableLeave(): void {
    this.hoveredTableId = null;
  }

  onTableClick(table: Table): void {
    if (table.isAvailable) {
      this.tableSelect.emit(table.id);
    }
  }

  getSelectedTable(): Table | undefined {
    return this.tables.find(t => t.id === this.selectedTableId);
  }
}
