<div class="container mx-auto px-4 py-12">
  <h1 class="text-3xl font-bold text-center mb-8">Mon Compte</h1>

  <!-- Tabs -->
  <div *ngIf="!currentUser" class="flex justify-center mb-8">
    <div class="flex space-x-4">
      <button
        (click)="setActiveTab('login')"
        class="px-6 py-2 rounded-md transition-colors"
        [ngClass]="{'bg-gold-600 text-white': activeTab === 'login', 'bg-gray-200 text-gray-700 hover:bg-gray-300': activeTab !== 'login'}"
      >
        Connexion
      </button>
      <button
        (click)="setActiveTab('register')"
        class="px-6 py-2 rounded-md transition-colors"
        [ngClass]="{'bg-gold-600 text-white': activeTab === 'register', 'bg-gray-200 text-gray-700 hover:bg-gray-300': activeTab !== 'register'}"
      >
        Inscription
      </button>
      <button
        (click)="setActiveTab('forgot-password')"
        class="px-6 py-2 rounded-md transition-colors"
        [ngClass]="{'bg-gold-600 text-white': activeTab === 'forgot-password', 'bg-gray-200 text-gray-700 hover:bg-gray-300': activeTab !== 'forgot-password'}"
      >
        Mot de passe oublié
      </button>
    </div>
  </div>

  <!-- Success and Error Messages -->
  <div *ngIf="successMessage" class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
    {{ successMessage }}
  </div>
  <div *ngIf="errorMessage" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
    {{ errorMessage }}
  </div>

  <!-- Login Form -->
  <div *ngIf="activeTab === 'login' && !currentUser" class="max-w-md mx-auto bg-white p-8 rounded-lg shadow-md">
    <h2 class="text-2xl font-semibold mb-6">Connexion</h2>
    <form [formGroup]="loginForm" (ngSubmit)="onLogin()" class="space-y-6">
      <div>
        <label for="login-email" class="block text-sm font-medium text-gray-700 mb-1">
          Email
        </label>
        <input
          id="login-email"
          type="email"
          formControlName="email"
          class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
          [ngClass]="{'border-red-500': loginForm.get('email')?.invalid && loginForm.get('email')?.touched, 'border-gray-300': !(loginForm.get('email')?.invalid && loginForm.get('email')?.touched)}"
        />
        <div *ngIf="loginForm.get('email')?.invalid && loginForm.get('email')?.touched" class="mt-1 text-sm text-red-600">
          <div *ngIf="loginForm.get('email')?.errors?.['required']">L'email est requis.</div>
          <div *ngIf="loginForm.get('email')?.errors?.['email']">Veuillez entrer un email valide.</div>
        </div>
      </div>

      <div>
        <label for="login-password" class="block text-sm font-medium text-gray-700 mb-1">
          Mot de passe
        </label>
        <input
          id="login-password"
          type="password"
          formControlName="password"
          class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
          [ngClass]="{'border-red-500': loginForm.get('password')?.invalid && loginForm.get('password')?.touched, 'border-gray-300': !(loginForm.get('password')?.invalid && loginForm.get('password')?.touched)}"
        />
        <div *ngIf="loginForm.get('password')?.invalid && loginForm.get('password')?.touched" class="mt-1 text-sm text-red-600">
          <div *ngIf="loginForm.get('password')?.errors?.['required']">Le mot de passe est requis.</div>
          <div *ngIf="loginForm.get('password')?.errors?.['minlength']">Le mot de passe doit contenir au moins 6 caractères.</div>
        </div>
      </div>

      <div>
        <button
          type="submit"
          [disabled]="loginForm.invalid || isSubmitting"
          class="w-full bg-gold-600 hover:bg-gold-700 text-white py-2 px-4 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span *ngIf="!isSubmitting">Se connecter</span>
          <span *ngIf="isSubmitting" class="flex justify-center items-center">
            <span class="animate-spin h-5 w-5 mr-3 border-t-2 border-b-2 border-white rounded-full"></span>
            Connexion en cours...
          </span>
        </button>
      </div>

      <div class="text-center">
        <button
          type="button"
          (click)="setActiveTab('forgot-password')"
          class="text-gold-600 hover:text-gold-700 text-sm"
        >
          Mot de passe oublié?
        </button>
      </div>
    </form>
  </div>

  <!-- Register Form -->
  <div *ngIf="activeTab === 'register' && !currentUser" class="max-w-md mx-auto bg-white p-8 rounded-lg shadow-md">
    <h2 class="text-2xl font-semibold mb-6">Inscription</h2>
    <form [formGroup]="registerForm" (ngSubmit)="onRegister()" class="space-y-6">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label for="register-firstName" class="block text-sm font-medium text-gray-700 mb-1">
            Prénom
          </label>
          <input
            id="register-firstName"
            type="text"
            formControlName="firstName"
            class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
            [ngClass]="{'border-red-500': registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched, 'border-gray-300': !(registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched)}"
          />
          <div *ngIf="registerForm.get('firstName')?.invalid && registerForm.get('firstName')?.touched" class="mt-1 text-sm text-red-600">
            <div *ngIf="registerForm.get('firstName')?.errors?.['required']">Le prénom est requis.</div>
            <div *ngIf="registerForm.get('firstName')?.errors?.['minlength']">Le prénom doit contenir au moins 2 caractères.</div>
            <div *ngIf="registerForm.get('firstName')?.errors?.['maxlength']">Le prénom ne doit pas dépasser 50 caractères.</div>
            <div *ngIf="registerForm.get('firstName')?.errors?.['pattern']">Le prénom ne doit contenir que des lettres, espaces, apostrophes et tirets.</div>
          </div>
        </div>

        <div>
          <label for="register-lastName" class="block text-sm font-medium text-gray-700 mb-1">
            Nom
          </label>
          <input
            id="register-lastName"
            type="text"
            formControlName="lastName"
            class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
            [ngClass]="{'border-red-500': registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched, 'border-gray-300': !(registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched)}"
          />
          <div *ngIf="registerForm.get('lastName')?.invalid && registerForm.get('lastName')?.touched" class="mt-1 text-sm text-red-600">
            <div *ngIf="registerForm.get('lastName')?.errors?.['required']">Le nom est requis.</div>
            <div *ngIf="registerForm.get('lastName')?.errors?.['minlength']">Le nom doit contenir au moins 2 caractères.</div>
            <div *ngIf="registerForm.get('lastName')?.errors?.['maxlength']">Le nom ne doit pas dépasser 50 caractères.</div>
            <div *ngIf="registerForm.get('lastName')?.errors?.['pattern']">Le nom ne doit contenir que des lettres, espaces, apostrophes et tirets.</div>
          </div>
        </div>
      </div>

      <div>
        <label for="register-email" class="block text-sm font-medium text-gray-700 mb-1">
          Email
        </label>
        <input
          id="register-email"
          type="email"
          formControlName="email"
          class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
          [ngClass]="{'border-red-500': registerForm.get('email')?.invalid && registerForm.get('email')?.touched, 'border-gray-300': !(registerForm.get('email')?.invalid && registerForm.get('email')?.touched)}"
        />
        <div *ngIf="registerForm.get('email')?.invalid && registerForm.get('email')?.touched" class="mt-1 text-sm text-red-600">
          <div *ngIf="registerForm.get('email')?.errors?.['required']">L'email est requis.</div>
          <div *ngIf="registerForm.get('email')?.errors?.['email']">Veuillez entrer un email valide.</div>
        </div>
      </div>

      <div>
        <label for="register-phone" class="block text-sm font-medium text-gray-700 mb-1">
          Téléphone
        </label>
        <input
          id="register-phone"
          type="tel"
          formControlName="phoneNumber"
          class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
          [ngClass]="{'border-red-500': registerForm.get('phoneNumber')?.invalid && registerForm.get('phoneNumber')?.touched, 'border-gray-300': !(registerForm.get('phoneNumber')?.invalid && registerForm.get('phoneNumber')?.touched)}"
        />
        <div *ngIf="registerForm.get('phoneNumber')?.invalid && registerForm.get('phoneNumber')?.touched" class="mt-1 text-sm text-red-600">
          <div *ngIf="registerForm.get('phoneNumber')?.errors?.['required']">Le numéro de téléphone est requis.</div>
          <div *ngIf="registerForm.get('phoneNumber')?.errors?.['minlength']">Le numéro de téléphone doit contenir au moins 8 caractères.</div>
          <div *ngIf="registerForm.get('phoneNumber')?.errors?.['maxlength']">Le numéro de téléphone ne doit pas dépasser 20 caractères.</div>
          <div *ngIf="registerForm.get('phoneNumber')?.errors?.['pattern']">Veuillez entrer un numéro de téléphone valide.</div>
        </div>
      </div>

      <div>
        <label for="register-password" class="block text-sm font-medium text-gray-700 mb-1">
          Mot de passe
        </label>
        <input
          id="register-password"
          type="password"
          formControlName="password"
          class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
          [ngClass]="{'border-red-500': registerForm.get('password')?.invalid && registerForm.get('password')?.touched, 'border-gray-300': !(registerForm.get('password')?.invalid && registerForm.get('password')?.touched)}"
        />
        <div *ngIf="registerForm.get('password')?.invalid && registerForm.get('password')?.touched" class="mt-1 text-sm text-red-600">
          <div *ngIf="registerForm.get('password')?.errors?.['required']">Le mot de passe est requis.</div>
          <div *ngIf="registerForm.get('password')?.errors?.['minlength']">Le mot de passe doit contenir au moins 8 caractères.</div>
          <div *ngIf="registerForm.get('password')?.errors?.['maxlength']">Le mot de passe ne doit pas dépasser 50 caractères.</div>
          <div *ngIf="registerForm.get('password')?.errors?.['pattern']">
            Le mot de passe doit contenir au moins une lettre majuscule, une lettre minuscule et un chiffre.
          </div>
        </div>
      </div>

      <div>
        <label for="register-confirm-password" class="block text-sm font-medium text-gray-700 mb-1">
          Confirmer le mot de passe
        </label>
        <input
          id="register-confirm-password"
          type="password"
          formControlName="confirmPassword"
          class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
          [ngClass]="{'border-red-500': (registerForm.get('confirmPassword')?.invalid || registerForm.hasError('passwordMismatch')) && registerForm.get('confirmPassword')?.touched, 'border-gray-300': !((registerForm.get('confirmPassword')?.invalid || registerForm.hasError('passwordMismatch')) && registerForm.get('confirmPassword')?.touched)}"
        />
        <div *ngIf="(registerForm.get('confirmPassword')?.invalid || registerForm.hasError('passwordMismatch')) && registerForm.get('confirmPassword')?.touched" class="mt-1 text-sm text-red-600">
          <div *ngIf="registerForm.get('confirmPassword')?.errors?.['required']">La confirmation du mot de passe est requise.</div>
          <div *ngIf="registerForm.hasError('passwordMismatch')">Les mots de passe ne correspondent pas.</div>
        </div>
      </div>

      <div>
        <button
          type="submit"
          [disabled]="registerForm.invalid || isSubmitting"
          class="w-full bg-gold-600 hover:bg-gold-700 text-white py-2 px-4 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span *ngIf="!isSubmitting">S'inscrire</span>
          <span *ngIf="isSubmitting" class="flex justify-center items-center">
            <span class="animate-spin h-5 w-5 mr-3 border-t-2 border-b-2 border-white rounded-full"></span>
            Inscription en cours...
          </span>
        </button>
      </div>
    </form>
  </div>

  <!-- Forgot Password Form -->
  <div *ngIf="activeTab === 'forgot-password' && !currentUser" class="max-w-md mx-auto bg-white p-8 rounded-lg shadow-md">
    <h2 class="text-2xl font-semibold mb-6">Réinitialisation du mot de passe</h2>
    <form [formGroup]="forgotPasswordForm" (ngSubmit)="onForgotPassword()" class="space-y-6">
      <div>
        <label for="forgot-email" class="block text-sm font-medium text-gray-700 mb-1">
          Email
        </label>
        <input
          id="forgot-email"
          type="email"
          formControlName="email"
          class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
          [ngClass]="{'border-red-500': forgotPasswordForm.get('email')?.invalid && forgotPasswordForm.get('email')?.touched, 'border-gray-300': !(forgotPasswordForm.get('email')?.invalid && forgotPasswordForm.get('email')?.touched)}"
        />
        <div *ngIf="forgotPasswordForm.get('email')?.invalid && forgotPasswordForm.get('email')?.touched" class="mt-1 text-sm text-red-600">
          <div *ngIf="forgotPasswordForm.get('email')?.errors?.['required']">L'email est requis.</div>
          <div *ngIf="forgotPasswordForm.get('email')?.errors?.['email']">Veuillez entrer un email valide.</div>
        </div>
      </div>

      <div>
        <button
          type="submit"
          [disabled]="forgotPasswordForm.invalid || isSubmitting"
          class="w-full bg-gold-600 hover:bg-gold-700 text-white py-2 px-4 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span *ngIf="!isSubmitting">Réinitialiser le mot de passe</span>
          <span *ngIf="isSubmitting" class="flex justify-center items-center">
            <span class="animate-spin h-5 w-5 mr-3 border-t-2 border-b-2 border-white rounded-full"></span>
            Envoi en cours...
          </span>
        </button>
      </div>

      <div class="text-center">
        <button
          type="button"
          (click)="setActiveTab('login')"
          class="text-gold-600 hover:text-gold-700 text-sm"
        >
          Retour à la connexion
        </button>
      </div>
    </form>
  </div>

  <!-- Profile Section -->
  <div *ngIf="currentUser" class="max-w-4xl mx-auto">
    <div class="bg-white p-8 rounded-lg shadow-md">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-semibold">Mon Profil</h2>
        <div class="space-x-4">
          <button
            (click)="navigateToReservations()"
            class="bg-gray-200 hover:bg-gray-300 text-gray-800 py-2 px-4 rounded-md transition-colors"
          >
            Mes Réservations
          </button>
          <button
            (click)="onLogout()"
            class="bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-md transition-colors"
          >
            Déconnexion
          </button>
        </div>
      </div>

      <form [formGroup]="profileForm" (ngSubmit)="onUpdateProfile()" class="space-y-6">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label for="profile-firstName" class="block text-sm font-medium text-gray-700 mb-1">
              Prénom
            </label>
            <input
              id="profile-firstName"
              type="text"
              formControlName="firstName"
              class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
              [ngClass]="{'border-red-500': profileForm.get('firstName')?.invalid && profileForm.get('firstName')?.touched, 'border-gray-300': !(profileForm.get('firstName')?.invalid && profileForm.get('firstName')?.touched)}"
            />
            <div *ngIf="profileForm.get('firstName')?.invalid && profileForm.get('firstName')?.touched" class="mt-1 text-sm text-red-600">
              <div *ngIf="profileForm.get('firstName')?.errors?.['required']">Le prénom est requis.</div>
              <div *ngIf="profileForm.get('firstName')?.errors?.['minlength']">Le prénom doit contenir au moins 2 caractères.</div>
            </div>
          </div>

          <div>
            <label for="profile-lastName" class="block text-sm font-medium text-gray-700 mb-1">
              Nom
            </label>
            <input
              id="profile-lastName"
              type="text"
              formControlName="lastName"
              class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
              [ngClass]="{'border-red-500': profileForm.get('lastName')?.invalid && profileForm.get('lastName')?.touched, 'border-gray-300': !(profileForm.get('lastName')?.invalid && profileForm.get('lastName')?.touched)}"
            />
            <div *ngIf="profileForm.get('lastName')?.invalid && profileForm.get('lastName')?.touched" class="mt-1 text-sm text-red-600">
              <div *ngIf="profileForm.get('lastName')?.errors?.['required']">Le nom est requis.</div>
              <div *ngIf="profileForm.get('lastName')?.errors?.['minlength']">Le nom doit contenir au moins 2 caractères.</div>
            </div>
          </div>
        </div>

        <div>
          <label for="profile-email" class="block text-sm font-medium text-gray-700 mb-1">
            Email
          </label>
          <input
            id="profile-email"
            type="email"
            formControlName="email"
            class="w-full border rounded-md py-2 px-3 bg-gray-100"
            readonly
          />
          <p class="mt-1 text-sm text-gray-500">L'email ne peut pas être modifié.</p>
        </div>

        <div>
          <label for="profile-phone" class="block text-sm font-medium text-gray-700 mb-1">
            Téléphone
          </label>
          <input
            id="profile-phone"
            type="tel"
            formControlName="phoneNumber"
            class="w-full border rounded-md py-2 px-3 focus:outline-none focus:ring-2 focus:ring-gold-500"
            [ngClass]="{'border-red-500': profileForm.get('phoneNumber')?.invalid && profileForm.get('phoneNumber')?.touched, 'border-gray-300': !(profileForm.get('phoneNumber')?.invalid && profileForm.get('phoneNumber')?.touched)}"
          />
          <div *ngIf="profileForm.get('phoneNumber')?.invalid && profileForm.get('phoneNumber')?.touched" class="mt-1 text-sm text-red-600">
            <div *ngIf="profileForm.get('phoneNumber')?.errors?.['required']">Le numéro de téléphone est requis.</div>
            <div *ngIf="profileForm.get('phoneNumber')?.errors?.['minlength']">Le numéro de téléphone doit contenir au moins 8 caractères.</div>
          </div>
        </div>

        <div>
          <button
            type="submit"
            [disabled]="profileForm.invalid || isSubmitting"
            class="bg-gold-600 hover:bg-gold-700 text-white py-2 px-4 rounded-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <span *ngIf="!isSubmitting">Mettre à jour le profil</span>
            <span *ngIf="isSubmitting" class="flex justify-center items-center">
              <span class="animate-spin h-5 w-5 mr-3 border-t-2 border-b-2 border-white rounded-full"></span>
              Mise à jour en cours...
            </span>
          </button>
        </div>
      </form>
    </div>
  </div>
</div>
