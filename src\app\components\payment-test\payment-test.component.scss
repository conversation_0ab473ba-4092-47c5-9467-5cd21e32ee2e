/* Payment Test Component Styles */

.test-scenario-card {
  transition: all 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  }
}

.result-item {
  transition: all 0.3s ease-in-out;
  
  &.success {
    border-left: 4px solid #10b981;
  }
  
  &.error {
    border-left: 4px solid #ef4444;
  }
}

.test-button {
  transition: all 0.2s ease-in-out;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
  
  &:active {
    transform: translateY(0);
  }
}

.results-container {
  max-height: 400px;
  overflow-y: auto;
  
  &::-webkit-scrollbar {
    width: 6px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
    
    &:hover {
      background: #94a3b8;
    }
  }
}

.json-output {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 11px;
  line-height: 1.4;
  background: #f8fafc;
  padding: 8px;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  max-height: 100px;
  overflow-y: auto;
  word-break: break-all;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  
  &.success {
    background-color: #dcfce7;
    color: #166534;
  }
  
  &.error {
    background-color: #fef2f2;
    color: #991b1b;
  }
}

.test-configuration {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 1px solid #e2e8f0;
}

.payment-form-container {
  background: linear-gradient(135deg, #fefefe 0%, #f9fafb 100%);
  border: 1px solid #e5e7eb;
}

.results-panel {
  background: linear-gradient(135deg, #fafafa 0%, #f4f4f5 100%);
  border: 1px solid #e4e4e7;
}

/* Responsive adjustments */
@media (max-width: 1280px) {
  .results-container {
    max-height: 300px;
  }
}

@media (max-width: 768px) {
  .json-output {
    font-size: 10px;
    max-height: 80px;
  }
  
  .results-container {
    max-height: 250px;
  }
}
