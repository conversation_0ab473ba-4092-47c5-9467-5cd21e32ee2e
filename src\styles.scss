/* You can add global styles to this file, and also import other style files */

/* Tailwind CSS */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Import Material Icons font */
@import url('https://fonts.googleapis.com/icon?family=Material+Icons');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

/* Custom colors */
:root {
  --gold-50: #fff9e6;
  --gold-100: #ffedb3;
  --gold-200: #ffe180;
  --gold-300: #ffd54d;
  --gold-400: #e4bf47;
  --gold-500: #d4af37;
  --gold-600: #c4a028;
  --gold-700: #b39020;
  --gold-800: #a38018;
  --gold-900: #826512;
}

/* Global styles */
html, body {
  height: 100%;
  margin: 0;
  font-family: 'Roboto', 'Helvetica Neue', sans-serif;
  scroll-behavior: smooth;
}

body {
  background-color: #f8f9fa;
}

/* Custom classes */
.text-gold-50 { color: var(--gold-50); }
.text-gold-100 { color: var(--gold-100); }
.text-gold-200 { color: var(--gold-200); }
.text-gold-300 { color: var(--gold-300); }
.text-gold-400 { color: var(--gold-400); }
.text-gold-500 { color: var(--gold-500); }
.text-gold-600 { color: var(--gold-600); }
.text-gold-700 { color: var(--gold-700); }
.text-gold-800 { color: var(--gold-800); }
.text-gold-900 { color: var(--gold-900); }

.bg-gold-50 { background-color: var(--gold-50); }
.bg-gold-100 { background-color: var(--gold-100); }
.bg-gold-200 { background-color: var(--gold-200); }
.bg-gold-300 { background-color: var(--gold-300); }
.bg-gold-400 { background-color: var(--gold-400); }
.bg-gold-500 { background-color: var(--gold-500); }
.bg-gold-600 { background-color: var(--gold-600); }
.bg-gold-700 { background-color: var(--gold-700); }
.bg-gold-800 { background-color: var(--gold-800); }
.bg-gold-900 { background-color: var(--gold-900); }

.hover\:bg-gold-500:hover { background-color: var(--gold-500); }
.hover\:bg-gold-600:hover { background-color: var(--gold-600); }
.hover\:bg-gold-700:hover { background-color: var(--gold-700); }
.hover\:bg-gold-800:hover { background-color: var(--gold-800); }

.hover\:text-gold-500:hover { color: var(--gold-500); }
.hover\:text-gold-600:hover { color: var(--gold-600); }
.hover\:text-gold-700:hover { color: var(--gold-700); }

.border-gold-500 { border-color: var(--gold-500); }
.border-gold-600 { border-color: var(--gold-600); }
.border-gold-700 { border-color: var(--gold-700); }
.border-gold-800 { border-color: var(--gold-800); }

.focus\:ring-gold-500:focus { --tw-ring-color: var(--gold-500); }
.focus\:ring-gold-600:focus { --tw-ring-color: var(--gold-600); }

/* Material Icons */
.material-icons {
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  font-size: 24px;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(20px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideInUp 0.5s ease-in-out;
}

.animate-slide-right {
  animation: slideInRight 0.5s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .container {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  h1 {
    font-size: 1.75rem !important;
  }

  h2 {
    font-size: 1.5rem !important;
  }

  .py-12 {
    padding-top: 2rem !important;
    padding-bottom: 2rem !important;
  }

  .px-6 {
    padding-left: 1rem !important;
    padding-right: 1rem !important;
  }

  .space-x-4 {
    gap: 0.5rem !important;
  }

  .grid-cols-2 {
    grid-template-columns: 1fr !important;
  }

  .md\:grid-cols-2 {
    grid-template-columns: 1fr !important;
  }
}

/* Form styles */
input:focus, textarea:focus, select:focus {
  outline: none;
  border-color: var(--gold-500);
  box-shadow: 0 0 0 2px rgba(212, 175, 55, 0.2);
}

button {
  transition: all 0.2s ease-in-out;
}

button:hover {
  transform: translateY(-1px);
}

button:active {
  transform: translateY(1px);
}

/* Card hover effects */
.card-hover {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Loading spinner */
.spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border-left-color: var(--gold-600);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Toast notifications */
.toast {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 1rem;
  background: white;
  color: #333;
  border-radius: 4px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  animation: slideInUp 0.3s ease-out;
}

.toast-success {
  border-left: 4px solid #10b981;
}

.toast-error {
  border-left: 4px solid #ef4444;
}

.toast-info {
  border-left: 4px solid #3b82f6;
}
