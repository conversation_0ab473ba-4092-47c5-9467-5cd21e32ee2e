import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';

import { PaymentFormComponent } from '../payment-form/payment-form.component';
import { PaymentService } from '../../services/payment.service';

interface TestScenario {
  name: string;
  description: string;
  amount: number;
  reservationId: string;
  shouldFail: boolean;
  expectedError?: string;
}

@Component({
  selector: 'app-payment-test',
  standalone: true,
  imports: [CommonModule, FormsModule, PaymentFormComponent],
  templateUrl: './payment-test.component.html',
  styleUrls: ['./payment-test.component.scss']
})
export class PaymentTestComponent {
  // Test scenarios
  testScenarios: TestScenario[] = [
    {
      name: 'Paiement réussi - Carte',
      description: 'Test d\'un paiement par carte réussi avec un montant standard',
      amount: 150,
      reservationId: 'test-reservation-success',
      shouldFail: false
    },
    {
      name: '<PERSON><PERSON><PERSON> réussi - PayPal',
      description: 'Test d\'un paiement PayPal réussi',
      amount: 200,
      reservationId: 'test-reservation-paypal',
      shouldFail: false
    },
    {
      name: 'Montant élevé',
      description: 'Test avec un montant élevé pour vérifier la gestion',
      amount: 1000,
      reservationId: 'test-reservation-high-amount',
      shouldFail: false
    },
    {
      name: 'Montant minimal',
      description: 'Test avec un montant minimal',
      amount: 10,
      reservationId: 'test-reservation-minimal',
      shouldFail: false
    }
  ];

  // Current test state
  selectedScenario: TestScenario = this.testScenarios[0];
  customAmount = 100;
  customReservationId = 'test-custom-reservation';
  useCustomValues = false;

  // Test results
  testResults: Array<{
    scenario: string;
    success: boolean;
    paymentId?: string;
    error?: string;
    timestamp: Date;
  }> = [];

  // Payment service test results
  serviceTestResults: Array<{
    method: string;
    success: boolean;
    result?: any;
    error?: string;
    timestamp: Date;
  }> = [];

  constructor(private paymentService: PaymentService) {}

  selectScenario(scenario: TestScenario): void {
    this.selectedScenario = scenario;
    this.useCustomValues = false;
  }

  getCurrentAmount(): number {
    return this.useCustomValues ? this.customAmount : this.selectedScenario.amount;
  }

  getCurrentReservationId(): string {
    return this.useCustomValues ? this.customReservationId : this.selectedScenario.reservationId;
  }

  onPaymentSuccess(paymentId: string): void {
    const result = {
      scenario: this.useCustomValues ? 'Custom Values' : this.selectedScenario.name,
      success: true,
      paymentId,
      timestamp: new Date()
    };
    
    this.testResults.unshift(result);
    console.log('Payment Test Success:', result);
  }

  onPaymentError(error: string): void {
    const result = {
      scenario: this.useCustomValues ? 'Custom Values' : this.selectedScenario.name,
      success: false,
      error,
      timestamp: new Date()
    };
    
    this.testResults.unshift(result);
    console.log('Payment Test Error:', result);
  }

  // Direct service testing methods
  testCreatePaymentIntent(): void {
    const amount = this.getCurrentAmount();
    const reservationId = this.getCurrentReservationId();

    this.paymentService.createPaymentIntent(amount, reservationId).subscribe({
      next: (result) => {
        this.serviceTestResults.unshift({
          method: 'createPaymentIntent',
          success: true,
          result,
          timestamp: new Date()
        });
      },
      error: (error) => {
        this.serviceTestResults.unshift({
          method: 'createPaymentIntent',
          success: false,
          error: error.message,
          timestamp: new Date()
        });
      }
    });
  }

  testCreatePayPalOrder(): void {
    const amount = this.getCurrentAmount();
    const reservationId = this.getCurrentReservationId();

    this.paymentService.createPayPalOrder(amount, reservationId).subscribe({
      next: (result) => {
        this.serviceTestResults.unshift({
          method: 'createPayPalOrder',
          success: true,
          result,
          timestamp: new Date()
        });
      },
      error: (error) => {
        this.serviceTestResults.unshift({
          method: 'createPayPalOrder',
          success: false,
          error: error.message,
          timestamp: new Date()
        });
      }
    });
  }

  testConfirmCardPayment(): void {
    // First create a payment intent, then confirm it
    const amount = this.getCurrentAmount();
    const reservationId = this.getCurrentReservationId();

    this.paymentService.createPaymentIntent(amount, reservationId).subscribe({
      next: ({ paymentIntentId }) => {
        this.paymentService.confirmCardPayment(paymentIntentId).subscribe({
          next: (result) => {
            this.serviceTestResults.unshift({
              method: 'confirmCardPayment',
              success: true,
              result,
              timestamp: new Date()
            });
          },
          error: (error) => {
            this.serviceTestResults.unshift({
              method: 'confirmCardPayment',
              success: false,
              error: error.message,
              timestamp: new Date()
            });
          }
        });
      },
      error: (error) => {
        this.serviceTestResults.unshift({
          method: 'confirmCardPayment (createPaymentIntent failed)',
          success: false,
          error: error.message,
          timestamp: new Date()
        });
      }
    });
  }

  testCapturePayPalOrder(): void {
    // First create a PayPal order, then capture it
    const amount = this.getCurrentAmount();
    const reservationId = this.getCurrentReservationId();

    this.paymentService.createPayPalOrder(amount, reservationId).subscribe({
      next: ({ orderId }) => {
        this.paymentService.capturePayPalOrder(orderId).subscribe({
          next: (result) => {
            this.serviceTestResults.unshift({
              method: 'capturePayPalOrder',
              success: true,
              result,
              timestamp: new Date()
            });
          },
          error: (error) => {
            this.serviceTestResults.unshift({
              method: 'capturePayPalOrder',
              success: false,
              error: error.message,
              timestamp: new Date()
            });
          }
        });
      },
      error: (error) => {
        this.serviceTestResults.unshift({
          method: 'capturePayPalOrder (createPayPalOrder failed)',
          success: false,
          error: error.message,
          timestamp: new Date()
        });
      }
    });
  }

  clearResults(): void {
    this.testResults = [];
    this.serviceTestResults = [];
  }

  exportResults(): void {
    const results = {
      paymentFormTests: this.testResults,
      serviceTests: this.serviceTestResults,
      exportedAt: new Date()
    };
    
    const blob = new Blob([JSON.stringify(results, null, 2)], { type: 'application/json' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `payment-test-results-${new Date().toISOString().split('T')[0]}.json`;
    a.click();
    window.URL.revokeObjectURL(url);
  }
}
